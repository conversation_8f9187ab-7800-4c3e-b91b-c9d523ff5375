/**
 * 阶段7真实功能测试
 */

const UserManager = require("./core/业务服务/user-manager.js");

// 模拟依赖
const mockDatabase = {};
const mockConfig = {};
const userManager = new UserManager(mockDatabase, mockConfig);

console.log("🧪 阶段7真实功能测试开始\n");

// 测试用例1：给朋友看看机会
console.log("📋 测试用例1：给朋友看看机会");
const testMessage1 = "我想给朋友看看机会";
try {
  const result1 = userManager.detectThirdPartyRequest(testMessage1);
  console.log("输入:", testMessage1);
  console.log("结果:", result1);
  console.log("预期: isThirdParty=true, targetRole=朋友");

  const pass1 = result1.isThirdParty === true && result1.targetRole === "朋友";
  console.log("✅ 测试通过:", pass1);
  if (!pass1) {
    console.error("❌ 测试1失败");
    process.exit(1);
  }
} catch (error) {
  console.error("❌ 测试1异常:", error);
  process.exit(1);
}
console.log("");

// 测试用例2：给同事推荐
console.log("📋 测试用例2：给同事推荐");
const testMessage2 = "帮同事找个工作";
try {
  const result2 = userManager.detectThirdPartyRequest(testMessage2);
  console.log("输入:", testMessage2);
  console.log("结果:", result2);
  console.log("预期: isThirdParty=true, targetRole=同事");

  const pass2 = result2.isThirdParty === true && result2.targetRole === "同事";
  console.log("✅ 测试通过:", pass2);
  if (!pass2) {
    console.error("❌ 测试2失败");
    process.exit(1);
  }
} catch (error) {
  console.error("❌ 测试2异常:", error);
  process.exit(1);
}
console.log("");

// 测试用例3：给家人看看
console.log("📋 测试用例3：给家人看看");
const testMessage3 = "给家人看看有什么好机会";
try {
  const result3 = userManager.detectThirdPartyRequest(testMessage3);
  console.log("输入:", testMessage3);
  console.log("结果:", result3);
  console.log("预期: isThirdParty=true, targetRole=家人");

  const pass3 = result3.isThirdParty === true && result3.targetRole === "家人";
  console.log("✅ 测试通过:", pass3);
  if (!pass3) {
    console.error("❌ 测试3失败");
    process.exit(1);
  }
} catch (error) {
  console.error("❌ 测试3异常:", error);
  process.exit(1);
}
console.log("");

// 测试用例4：非第三方请求
console.log("📋 测试用例4：非第三方请求");
const testMessage4 = "我想找个工作";
try {
  const result4 = userManager.detectThirdPartyRequest(testMessage4);
  console.log("输入:", testMessage4);
  console.log("结果:", result4);
  console.log("预期: isThirdParty=false, targetRole=null");

  const pass4 = result4.isThirdParty === false && result4.targetRole === null;
  console.log("✅ 测试通过:", pass4);
  if (!pass4) {
    console.error("❌ 测试4失败");
    process.exit(1);
  }
} catch (error) {
  console.error("❌ 测试4异常:", error);
  process.exit(1);
}
console.log("");

// 测试用例5：空消息
console.log("📋 测试用例5：空消息");
try {
  const result5 = userManager.detectThirdPartyRequest("");
  console.log("输入: (空字符串)");
  console.log("结果:", result5);
  console.log("预期: isThirdParty=false, targetRole=null");

  const pass5 = result5.isThirdParty === false && result5.targetRole === null;
  console.log("✅ 测试通过:", pass5);
  if (!pass5) {
    console.error("❌ 测试5失败");
    process.exit(1);
  }
} catch (error) {
  console.error("❌ 测试5异常:", error);
  process.exit(1);
}

console.log("\n🎯 阶段7真实功能测试全部通过！");
console.log("✅ detectThirdPartyRequest方法正常工作");
console.log("✅ 支持模糊匹配和同义词识别");
console.log("✅ 返回对象格式正确");
