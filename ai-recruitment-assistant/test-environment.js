/**
 * 阶段1：环境配置验证测试脚本
 * 验证数据库连接和AI API调用
 */

const { createClient } = require("@supabase/supabase-js");
const axios = require("axios");
require("dotenv").config({ path: "../.env" });

class EnvironmentValidator {
  constructor() {
    this.results = {
      env_check: false,
      database_connection: false,
      deepseek_api: false,
      qwen_api: false,
      response_times: {},
    };
  }

  /**
   * 检查环境变量配置
   */
  checkEnvironmentVariables() {
    console.log("🔍 检查环境变量配置...");

    const requiredVars = [
      "SUPABASE_URL",
      "SUPABASE_ANON_KEY",
      "DEEPSEEK_API_KEY",
      "QWEN_API_KEY",
    ];

    let allPresent = true;
    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        console.error(`❌ 缺少环境变量: ${varName}`);
        allPresent = false;
      } else {
        console.log(`✅ ${varName}: 已配置`);
      }
    }

    this.results.env_check = allPresent;
    return allPresent;
  }

  /**
   * 测试数据库连接
   */
  async testDatabaseConnection() {
    console.log("\n🗄️ 测试数据库连接...");

    try {
      const startTime = Date.now();

      const client = createClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_ANON_KEY
      );

      // 测试简单查询
      const { data, error } = await client
        .from("users")
        .select("count")
        .limit(1);

      const responseTime = Date.now() - startTime;
      this.results.response_times.database = responseTime;

      if (error) {
        throw error;
      }

      console.log(`✅ 数据库连接成功 (${responseTime}ms)`);
      this.results.database_connection = true;
      return true;
    } catch (error) {
      console.error(`❌ 数据库连接失败:`, error.message);
      this.results.database_connection = false;
      return false;
    }
  }

  /**
   * 测试DeepSeek API调用
   */
  async testDeepSeekAPI() {
    console.log("\n🤖 测试DeepSeek API...");

    try {
      const startTime = Date.now();

      const response = await axios.post(
        process.env.DEEPSEEK_ENDPOINT,
        {
          model: process.env.DEEPSEEK_MODEL,
          messages: [
            {
              role: "user",
              content: "测试连接，请回复'连接成功'",
            },
          ],
          max_tokens: 50,
          temperature: 0.1,
        },
        {
          headers: {
            Authorization: `Bearer ${process.env.DEEPSEEK_API_KEY}`,
            "Content-Type": "application/json",
          },
          timeout: 10000,
        }
      );

      const responseTime = Date.now() - startTime;
      this.results.response_times.deepseek = responseTime;

      if (response.data && response.data.choices && response.data.choices[0]) {
        const reply = response.data.choices[0].message.content;
        console.log(`✅ DeepSeek API调用成功 (${responseTime}ms)`);
        console.log(`📝 回复内容: ${reply}`);
        this.results.deepseek_api = true;
        return true;
      } else {
        throw new Error("API响应格式异常");
      }
    } catch (error) {
      console.error(`❌ DeepSeek API调用失败:`, error.message);
      this.results.deepseek_api = false;
      return false;
    }
  }

  /**
   * 测试Qwen API调用
   */
  async testQwenAPI() {
    console.log("\n🧠 测试Qwen API...");

    try {
      const startTime = Date.now();

      const response = await axios.post(
        process.env.QWEN_ENDPOINT,
        {
          model: process.env.QWEN_MODEL,
          input: {
            prompt: "测试连接，请回复'连接成功'",
          },
          parameters: {
            max_tokens: 50,
            temperature: 0.1,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${process.env.QWEN_API_KEY}`,
            "Content-Type": "application/json",
          },
          timeout: 10000,
        }
      );

      const responseTime = Date.now() - startTime;
      this.results.response_times.qwen = responseTime;

      console.log("Qwen API响应:", JSON.stringify(response.data, null, 2));

      if (response.data && response.data.output && response.data.output.text) {
        const reply = response.data.output.text;
        console.log(`✅ Qwen API调用成功 (${responseTime}ms)`);
        console.log(`📝 回复内容: ${reply}`);
        this.results.qwen_api = true;
        return true;
      } else {
        throw new Error(`API响应格式异常: ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      console.error(`❌ Qwen API调用失败:`, error.message);
      if (error.response) {
        console.error("响应状态:", error.response.status);
        console.error(
          "响应数据:",
          JSON.stringify(error.response.data, null, 2)
        );
      }
      this.results.qwen_api = false;
      return false;
    }
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log("\n📊 ===== 环境配置验证报告 =====");
    console.log(
      `🔧 环境变量检查: ${this.results.env_check ? "✅ 通过" : "❌ 失败"}`
    );
    console.log(
      `🗄️ 数据库连接: ${this.results.database_connection ? "✅ 通过" : "❌ 失败"}`
    );
    console.log(
      `🤖 DeepSeek API: ${this.results.deepseek_api ? "✅ 通过" : "❌ 失败"}`
    );
    console.log(
      `🧠 Qwen API: ${this.results.qwen_api ? "✅ 通过" : "❌ 失败"}`
    );

    console.log("\n⏱️ 响应时间统计:");
    if (this.results.response_times.database) {
      console.log(`   数据库: ${this.results.response_times.database}ms`);
    }
    if (this.results.response_times.deepseek) {
      console.log(`   DeepSeek: ${this.results.response_times.deepseek}ms`);
    }
    if (this.results.response_times.qwen) {
      console.log(`   Qwen: ${this.results.response_times.qwen}ms`);
    }

    const allPassed =
      this.results.env_check &&
      this.results.database_connection &&
      this.results.deepseek_api &&
      this.results.qwen_api;

    console.log(`\n🎯 总体结果: ${allPassed ? "✅ 全部通过" : "❌ 存在问题"}`);

    if (!allPassed) {
      console.log("\n⚠️ 需要解决的问题:");
      if (!this.results.env_check) console.log("   - 环境变量配置不完整");
      if (!this.results.database_connection) console.log("   - 数据库连接失败");
      if (!this.results.deepseek_api) console.log("   - DeepSeek API调用失败");
      if (!this.results.qwen_api) console.log("   - Qwen API调用失败");
    }

    return allPassed;
  }

  /**
   * 运行完整验证
   */
  async runValidation() {
    console.log("🚀 开始环境配置验证...\n");

    // 1. 检查环境变量
    this.checkEnvironmentVariables();

    // 2. 测试数据库连接
    await this.testDatabaseConnection();

    // 3. 测试DeepSeek API
    await this.testDeepSeekAPI();

    // 4. 测试Qwen API
    await this.testQwenAPI();

    // 5. 生成报告
    return this.generateReport();
  }
}

// 运行验证
async function main() {
  const validator = new EnvironmentValidator();
  const success = await validator.runValidation();

  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = EnvironmentValidator;
