/**
 * AI招聘助手系统 - 消息处理器（主路由）
 *
 * 核心职责：
 * - 消息路由和意图识别
 * - 业务流程编排
 * - 上下文管理
 * - 错误处理和回退机制
 *
 * 预计代码量：2000行
 */

const { v4: uuidv4 } = require("uuid");

// 导入业务模块
const AIServices = require("../数据管理/ai-services");
const PassiveRecommender = require("../业务服务/passive-recommender");
const ActiveRecommender = require("../业务服务/active-recommender");
const TechMapper = require("../业务服务/tech-mapper");
const Utilities = require("../工具库/utilities");
const Validators = require("../工具库/validators");

class MessageProcessor {
  constructor({ database, userManager, config }) {
    this.database = database;
    this.userManager = userManager;
    this.config = config;

    // 业务模块
    this.aiServices = null;
    this.passiveRecommender = null;
    this.activeRecommender = null;
    this.techMapper = null;

    // 工具模块
    this.utilities = new Utilities();
    this.validators = new Validators();

    // 意图类型定义
    this.intentTypes = {
      GREETING: "greeting",
      PROFILE_UPDATE: "profile_update",
      JOB_SEARCH: "job_search",
      RECOMMENDATION_REQUEST: "recommendation_request",
      TECH_DIRECTION_INQUIRY: "tech_direction_inquiry",
      SALARY_INQUIRY: "salary_inquiry",
      COMPANY_INQUIRY: "company_inquiry",
      LOCATION_INQUIRY: "location_inquiry",
      EXPERIENCE_INQUIRY: "experience_inquiry",
      RESUME_UPLOAD: "resume_upload",
      JOB_DETAIL_REQUEST: "job_detail_request",
      UNKNOWN: "unknown",
    };

    // 上下文状态
    this.contextStates = {
      INITIAL: "initial",
      PROFILE_BUILDING: "profile_building",
      JOB_SEARCHING: "job_searching",
      RECOMMENDATION_VIEWING: "recommendation_viewing",
      CLARIFICATION_NEEDED: "clarification_needed",
    };
  }

  /**
   * 初始化消息处理器
   */
  async initialize() {
    try {
      // 初始化AI服务
      this.aiServices = new AIServices(this.config.getAIConfig());
      await this.aiServices.initialize();

      // 初始化推荐引擎
      this.passiveRecommender = new PassiveRecommender(
        this.database,
        this.config
      );
      await this.passiveRecommender.initialize();

      this.activeRecommender = new ActiveRecommender(
        this.database,
        this.config
      );
      await this.activeRecommender.initialize();

      // 初始化技术映射器
      this.techMapper = new TechMapper(this.database, this.config);
      await this.techMapper.initialize();

      // 初始化用户管理器的意图识别功能
      this.userManager.initializeIntentRecognizer(
        this.aiServices,
        this.utilities,
        this.database
      );

      console.log("🧠 消息处理器初始化完成");
    } catch (error) {
      console.error("❌ 消息处理器初始化失败:", error);
      throw error;
    }
  }

  /**
   * 处理用户消息 - 主入口
   */
  async processMessage(messageData) {
    try {
      // 1. 验证输入数据
      const validatedData = this.validators.validateMessageInput(messageData);

      // 2. 检查是否是初始化请求
      if (validatedData.message === "__INIT__") {
        return await this.handleInitRequest(validatedData);
      }

      // 3. 获取或创建会话
      const session = await this.getOrCreateSession(validatedData);

      // 4. 检查是否是第一条用户消息（在保存之前检查）
      const isFirstUserMessage = await this.isFirstUserMessage(session.id);

      // 5. 保存用户消息
      await this.saveUserMessage(session.id, validatedData.message);

      // 6. 分析用户意图（使用用户管理器中的意图识别功能）
      const intent = await this.userManager.analyzeUserIntent(
        validatedData.message,
        session,
        isFirstUserMessage
      );

      // 6. 根据意图路由到相应处理器
      const response = await this.routeToHandler(
        intent,
        validatedData,
        session,
        isFirstUserMessage
      );

      // 6. 保存助手回复
      await this.saveAssistantMessage(session.id, response);

      // 7. 更新会话状态
      await this.updateSessionContext(session.id, intent, response);

      return {
        success: true,
        sessionId: session.session_uuid,
        response: response,
        intent: intent.type,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("❌ 消息处理失败:", error);

      // 返回错误回退响应
      return {
        success: false,
        error: error.message,
        response: this.getErrorFallbackResponse(),
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * 处理初始化请求
   */
  async handleInitRequest(messageData) {
    try {
      // 获取或创建会话
      const session = await this.getOrCreateSession(messageData);

      // 返回开场白
      return {
        success: true,
        sessionId: session.session_uuid,
        response: {
          type: "welcome",
          content: `您好，我是AI领域的猎头Katrina，专注于AI算法职位。
聊天框的左下角有简历上传的按钮，您可以分享最新的简历，便于我更精准的给您推荐职位。
同时，您也可以点击左下角的邮箱按钮，分享您的个人邮箱，我们可以更好的保持联系，定期的给您推送合适的职位。`,
          metadata: {
            isWelcome: true,
            timestamp: new Date().toISOString(),
          },
        },
        intent: "welcome",
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("❌ 处理初始化请求失败:", error);
      return {
        success: false,
        error: "初始化失败",
        response: {
          type: "error",
          content: "抱歉，系统初始化失败，请刷新页面重试。",
        },
      };
    }
  }

  /**
   * 获取或创建会话（支持跨设备同步）
   */
  async getOrCreateSession(messageData) {
    try {
      let session = null;

      // 如果提供了sessionId，尝试获取现有会话
      if (messageData.sessionId) {
        // 检查是否是加密token（长度超过36的通常是加密token）
        if (messageData.sessionId.length > 36) {
          // 尝试解密token获取用户信息
          const utilities = require("../工具库/utilities");
          const util = new utilities();
          const tokenData = util.parseSessionToken(messageData.sessionId);

          if (tokenData && tokenData.email) {
            // 使用token中的email查找用户的活跃会话
            const user = await this.userManager.getOrCreateUser(
              tokenData.email
            );
            session = await this.findActiveUserSession(user.id);
          }
        } else {
          // 普通sessionId，直接查询
          session = await this.database.getSessionByUuid(messageData.sessionId);
          console.log(
            `🔍 查询会话结果: sessionId=${messageData.sessionId}, found=${!!session}`
          );
        }

        // 检查会话是否过期（从配置读取天数）
        if (session) {
          const sessionTimeoutDays =
            this.config.getBusinessConfig().sessionTimeout || 14;
          const cutoffDate = new Date();
          cutoffDate.setDate(cutoffDate.getDate() - sessionTimeoutDays);

          if (new Date(session.last_active_at) < cutoffDate) {
            // 会话已过期，清理并创建新会话
            await this.cleanExpiredSession(session);
            session = null;
          }
        }
      }

      // 如果没有找到会话，创建新会话
      if (!session) {
        // 获取或创建用户
        const user = await this.userManager.getOrCreateUser(
          messageData.userEmail
        );

        // 检查用户是否有其他活跃会话（跨设备同步）
        // 暂时禁用跨设备同步来测试第一句回复
        // const activeSession = await this.findActiveUserSession(user.id);

        // if (activeSession) {
        //   // 使用现有活跃会话实现跨设备同步
        //   session = activeSession;
        // } else {
        // 创建新会话 - 如果前端提供了sessionId且格式正确，使用它；否则生成新的UUID
        let sessionUuid;
        if (
          messageData.sessionId &&
          messageData.sessionId.length <= 36 &&
          !messageData.sessionId.startsWith("session-")
        ) {
          // 前端提供了有效的UUID格式sessionId，使用它
          sessionUuid = messageData.sessionId;
        } else {
          // 生成新的UUID
          sessionUuid = uuidv4();
        }

        session = await this.database.createChatSession({
          userId: user.id,
          sessionUuid: sessionUuid,
          entrySourceUrl: messageData.entrySource || "",
          initialIntent: "",
          context: {
            state: this.contextStates.INITIAL,
            lastIntent: null,
            profileCompleteness: 0,
          },
        });

        // 发送开场白
        await this.sendWelcomeMessage(session.id, user.name || "");
        // }
      }

      return session;
    } catch (error) {
      console.error("❌ 获取或创建会话失败:", error);
      throw error;
    }
  }

  /**
   * 查找用户的活跃会话（跨设备同步）
   */
  async findActiveUserSession(userId) {
    try {
      const sessionTimeoutDays =
        this.config.getBusinessConfig().sessionTimeout || 14;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - sessionTimeoutDays);

      const { data: sessions, error } = await this.database.client
        .from("chat_sessions")
        .select("*")
        .eq("user_id", userId)
        .gte("last_active_at", cutoffDate.toISOString())
        .order("last_active_at", { ascending: false })
        .limit(1);

      if (error) throw error;

      return sessions && sessions.length > 0 ? sessions[0] : null;
    } catch (error) {
      console.error("❌ 查找活跃会话失败:", error);
      return null;
    }
  }

  /**
   * 清理过期会话
   */
  async cleanExpiredSession(session) {
    try {
      // 删除会话相关消息
      await this.database.client
        .from("chat_messages")
        .delete()
        .eq("session_id", session.id);

      // 删除会话
      await this.database.client
        .from("chat_sessions")
        .delete()
        .eq("id", session.id);

      console.log(`🗑️ 清理过期会话: ${session.session_uuid}`);
    } catch (error) {
      console.error("❌ 清理过期会话失败:", error);
    }
  }

  /**
   * 检查是否是用户第一次发言
   */
  async isFirstUserMessage(sessionId) {
    try {
      const { data: messages, error } = await this.database.client
        .from("chat_messages")
        .select("id")
        .eq("session_id", sessionId)
        .eq("message_type", "user")
        .limit(1);

      if (error) throw error;

      // 如果没有用户消息记录，说明这是第一次
      const isFirst = !messages || messages.length === 0;
      console.log(
        `🔍 检查第一句用户消息 - 会话ID: ${sessionId}, 用户消息数: ${messages?.length || 0}, 是否第一句: ${isFirst}`
      );
      return isFirst;
    } catch (error) {
      console.error("❌ 检查用户消息历史失败:", error);
      return false;
    }
  }

  /**
   * 根据意图路由到相应处理器
   */
  async routeToHandler(
    intent,
    messageData,
    session,
    isFirstUserMessage = null
  ) {
    try {
      switch (intent.type) {
        case this.intentTypes.GREETING:
          return await this.handleGreeting(
            intent,
            messageData,
            session,
            isFirstUserMessage
          );

        case this.intentTypes.PROFILE_UPDATE:
          return await this.handleProfileUpdate(intent, messageData, session);

        case this.intentTypes.JOB_SEARCH:
          return await this.handleJobSearch(
            intent,
            messageData,
            session,
            isFirstUserMessage
          );

        case this.intentTypes.RECOMMENDATION_REQUEST:
          return await this.handleRecommendationRequest(
            intent,
            messageData,
            session
          );

        case this.intentTypes.TECH_DIRECTION_INQUIRY:
          return await this.handleTechDirectionInquiry(
            intent,
            messageData,
            session
          );

        case this.intentTypes.SALARY_INQUIRY:
          return await this.handleSalaryInquiry(intent, messageData, session);

        case this.intentTypes.COMPANY_INQUIRY:
          return await this.handleCompanyInquiry(intent, messageData, session);

        case this.intentTypes.JOB_DETAIL_REQUEST:
          return await this.handleJobDetailRequest(
            intent,
            messageData,
            session
          );

        default:
          return await this.handleUnknownIntent(messageData, session);
      }
    } catch (error) {
      console.error("❌ 意图处理失败:", error);
      return this.getErrorFallbackResponse();
    }
  }

  /**
   * 处理问候语
   */
  async handleGreeting(
    intent,
    messageData,
    session,
    isFirstUserMessage = null
  ) {
    try {
      // 如果没有传入isFirstUserMessage，则检查
      if (isFirstUserMessage === null) {
        isFirstUserMessage = await this.isFirstUserMessage(session.id);
      }

      if (isFirstUserMessage) {
        // 第一句回复的特殊处理
        return await this.handleFirstUserGreeting(messageData, session, intent);
      }

      // 非第一句回复：检查用户档案完整性
      const user = await this.userManager.getUserBySession(session);
      const profile = await this.database.getCandidateProfile(user.id);

      if (!profile) {
        return {
          type: "greeting_new_user",
          content:
            "您好！我需要了解一些基本信息来为您推荐合适的职位。请告诉我您的技术方向和工作经验？",
          suggestions: [
            "我是前端开发工程师",
            "我做后端开发",
            "我是全栈工程师",
            "我想了解更多",
          ],
        };
      } else {
        return {
          type: "greeting_returning_user",
          content: `欢迎回来！我记得您是${profile.candidate_tech_direction_raw || "技术"}方向的。今天想要什么帮助吗？`,
          suggestions: [
            "推荐职位给我",
            "更新我的信息",
            "查看薪资行情",
            "了解公司信息",
          ],
        };
      }
    } catch (error) {
      console.error("❌ 处理问候语失败:", error);
      return this.getErrorFallbackResponse();
    }
  }

  /**
   * 处理第一句用户回复（开场白后的第一句）
   */
  async handleFirstUserGreeting(messageData, session, intent) {
    try {
      const message = messageData.message.toLowerCase().trim();

      // 1. 纯问候类 - 硬编码回复
      if (this.isGreetingMessage(message)) {
        return {
          type: "first_greeting",
          content: "您考虑看看新机会吗？优质的职位还挺多的。",
          metadata: {
            isFirstResponse: true,
            responseSource: "hardcoded_greeting",
          },
        };
      }

      // 2. 职位/公司询问类 - 硬编码回复（立即返回两条消息）
      if (this.isJobInquiryMessage(message)) {
        console.log("✅ 识别为职位询问消息，开始保存第二条消息到数据库");
        // 延迟保存第二条消息，确保时间戳晚于第一条消息
        setTimeout(async () => {
          try {
            await this.database.saveChatMessage({
              sessionId: session.id,
              messageType: "assistant",
              content:
                "得拜托您告知我，您的技术栈或技术方向、当前所在公司、当前的职级、期望的薪资？",
              metadata: {
                type: "follow_up_message",
                timestamp: new Date().toISOString(),
              },
            });
            console.log("✅ 第二条消息已延迟保存到数据库");
          } catch (error) {
            console.error("❌ 保存第二条消息失败:", error);
          }
        }, 1000); // 延迟1秒保存

        // 返回第一条消息，前端会重新获取消息列表看到两条
        return {
          type: "first_job_inquiry",
          content:
            "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。",
          metadata: {
            isFirstResponse: true,
            responseSource: "hardcoded_job_inquiry",
            hasFollowUpMessage: true,
          },
        };
      }

      // 3. 身份确认类 - 硬编码回复
      if (this.isIdentityCheckMessage(message)) {
        return {
          type: "first_identity_check",
          content:
            "我是Felton团队开发的AI猎头顾问，请您相信，我可以为您提供精准的职位推荐服务。",
          metadata: {
            isFirstResponse: true,
            responseSource: "hardcoded_identity",
          },
        };
      }

      // 4. 公司询问类 - 硬编码回复
      if (this.isCompanyInquiryMessage(message)) {
        return {
          type: "first_company_inquiry",
          content:
            "我们是一家猎头公司，名叫HLG，您可能在网上查不到，我们的规模不大，是属于小而美的公司。请您相信，我可以为您提供精准的职位推荐服务。",
          metadata: {
            isFirstResponse: true,
            responseSource: "hardcoded_company",
          },
        };
      }

      // 5. 其他情况 - 调用AI推理
      return await this.handleFirstMessageWithAI(messageData, session);
    } catch (error) {
      console.error("❌ 处理第一句回复失败:", error);
      return await this.handleFirstMessageWithAI(messageData, session);
    }
  }

  /**
   * 判断是否为问候消息
   */
  isGreetingMessage(message) {
    const greetingKeywords = ["你好", "hi", "hello", "哈喽", "在的", "在吗"];
    return greetingKeywords.some((keyword) => message.includes(keyword));
  }

  /**
   * 第一类：精确匹配的职位询问（纯硬编码）
   */
  isExactJobInquiry(message) {
    const exactPhrases = [
      "有什么职位推荐",
      "你有什么职位推荐",
      "有什么职位推荐的",
      "你有什么职位推荐的",
      "有什么职位推荐吗",
      "你有什么职位推荐吗",
      "有什么职位推荐呢",
      "你有什么职位推荐呢",
      "有什么工作推荐",
      "你有什么工作推荐",
      "有什么岗位推荐",
      "你有什么岗位推荐",
      "推荐一些职位",
      "推荐一些工作",
      "推荐一些岗位",
    ];
    return exactPhrases.some((phrase) => message.includes(phrase));
  }

  /**
   * 判断是否为职位/公司询问消息（兼容旧逻辑）
   */
  isJobInquiryMessage(message) {
    const jobKeywords = [
      "职位推荐",
      "公司推荐",
      "职位",
      "公司",
      "机会",
      "介绍",
      "帮我做什么",
      "推荐",
      "推荐的",
      "什么职位",
      "什么公司",
      "什么机会",
      "有什么职位",
      "有什么公司",
      "有什么机会",
      "有什么推荐",
      "有什么推荐的",
      "有职位",
      "有公司",
      "机会推荐",
      "介绍的",
    ];
    return jobKeywords.some((keyword) => message.includes(keyword));
  }

  /**
   * 第二类：AI辅助判断是否应该使用硬编码回复
   */
  async shouldUseHardcodedResponse(message, session) {
    try {
      const prompt = `用户消息："${message}"

请判断这个消息是否是在询问职位推荐或工作机会。

判断标准：
1. 用户是否在寻求工作机会、职位推荐、岗位信息？
2. 用户是否想了解有什么工作可以推荐？
3. 用户是否在表达求职意向？

请只回答 "是" 或 "否"，不要解释。`;

      const response = await this.aiServices.generateResponseWithDeepSeek(
        prompt,
        {
          maxTokens: 10,
          temperature: 0.1,
        }
      );

      return response && response.trim().includes("是");
    } catch (error) {
      console.error("❌ AI辅助判断失败:", error);
      // 出错时保守处理，不使用硬编码
      return false;
    }
  }

  /**
   * 获取硬编码的职位询问回复
   */
  async getHardcodedJobInquiryResponse(session) {
    // 延迟保存第二条消息，确保时间戳晚于第一条消息
    setTimeout(async () => {
      try {
        await this.database.saveChatMessage({
          sessionId: session.id,
          messageType: "assistant",
          content:
            "得拜托您告知我，您的技术栈或技术方向、当前所在公司、当前的职级、期望的薪资？",
          metadata: {
            type: "follow_up_message",
            timestamp: new Date().toISOString(),
          },
        });
        console.log("✅ 硬编码回复的第二条消息已延迟保存到数据库");
      } catch (error) {
        console.error("❌ 保存硬编码回复的第二条消息失败:", error);
      }
    }, 1000); // 延迟1秒保存

    // 返回第一条消息
    return {
      type: "job_inquiry",
      content:
        "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。",
      metadata: {
        responseSource: "hardcoded_job_inquiry",
        hasFollowUpMessage: true,
      },
    };
  }

  /**
   * 判断是否为身份确认消息
   */
  isIdentityCheckMessage(message) {
    const identityKeywords = ["你是谁", "ai", "机器人", "人工智能"];
    return identityKeywords.some((keyword) => message.includes(keyword));
  }

  /**
   * 判断是否为公司询问消息
   */
  isCompanyInquiryMessage(message) {
    const companyKeywords = [
      "什么公司的",
      "你们是什么公司",
      "哪个公司",
      "什么公司",
    ];
    return companyKeywords.some((keyword) => message.includes(keyword));
  }

  /**
   * 发送延迟消息
   */
  async sendDelayedMessage(sessionId, content, delayMs = 2000) {
    setTimeout(async () => {
      try {
        await this.database.saveChatMessage({
          sessionId: sessionId,
          messageType: "assistant",
          content: content,
          metadata: {
            type: "delayed_message",
            delay: delayMs,
            timestamp: new Date().toISOString(),
          },
        });
        console.log(`⏰ 延迟消息已发送: ${content.substring(0, 20)}...`);
      } catch (error) {
        console.error("❌ 发送延迟消息失败:", error);
      }
    }, delayMs);
  }

  /**
   * 使用AI处理第一句消息
   */
  async handleFirstMessageWithAI(messageData, session) {
    try {
      console.log("🤖 使用AI处理第一句消息:", messageData.message);

      const aiResponse = await this.aiServices.generateResponseWithDeepSeek(
        `用户在开场白后的第一句回复："${messageData.message}"。请作为AI猎头Katrina简洁回复，引导用户提供技术栈或技术方向、当前所在公司、当前的职级、期望的薪资等信息以便推荐职位。注意：不要在回复中称呼用户为"Katrina"，你自己就是Katrina。`,
        {
          isFirstMessage: true,
          sessionContext: session.current_interaction_context,
        }
      );

      return {
        type: "first_ai_response",
        content: aiResponse,
        metadata: {
          isFirstResponse: true,
          responseSource: "ai_inference",
        },
      };
    } catch (error) {
      console.error("❌ AI处理第一句消息失败:", error);
      return {
        type: "first_fallback",
        content:
          "感谢您的回复！为了给您推荐合适的职位，能否告诉我您的技术方向、目前公司、职级和期望薪酬呢？",
        metadata: {
          isFirstResponse: true,
          responseSource: "fallback",
        },
      };
    }
  }

  /**
   * 处理第一句信息提供类消息
   */
  async handleFirstInfoProviding(_messageData, _session, intent) {
    try {
      const extractedInfo = intent.extractedInfo || {};
      let confirmationText = "我了解到您";

      // 构建确认信息的文本
      const infoItems = [];
      if (extractedInfo.techStack && extractedInfo.techStack.length > 0) {
        infoItems.push(`技术栈是${extractedInfo.techStack.join("、")}`);
      }
      if (extractedInfo.experience) {
        infoItems.push(`有${extractedInfo.experience}年经验`);
      }
      if (extractedInfo.currentCompany) {
        infoItems.push(`目前在${extractedInfo.currentCompany}工作`);
      }

      if (infoItems.length > 0) {
        confirmationText += infoItems.join("，") + "。";
      } else {
        confirmationText = "感谢您提供的信息。";
      }

      return {
        type: "first_info_confirmation",
        content:
          confirmationText +
          "为了给您推荐更合适的职位，能否再告诉我您的期望薪酬和工作地点偏好？",
        metadata: {
          isFirstResponse: true,
          needsFollowUp: true,
          responseSource: "first_info_providing",
          extractedInfo: extractedInfo,
        },
        suggestions: [
          "我期望薪酬在30-50k",
          "我想在北京工作",
          "我对薪酬比较灵活",
          "我想了解更多职位",
        ],
      };
    } catch (error) {
      console.error("❌ 处理第一句信息提供失败:", error);
      return this.getDefaultFirstResponse();
    }
  }

  /**
   * 处理第一句其他类型消息（调用AI推理）
   */
  async handleFirstOtherMessage(messageData, session, intent) {
    try {
      // 调用AI服务进行推理
      const aiResponse = await this.aiServices.generateResponseWithDeepSeek(
        `用户第一次发送消息："${messageData.message}"，请作为AI招聘助手Katrina回复。如果用户的消息比较模糊或不清楚，可以说"您说的我没太明白，这是啥意思？不过没关系"然后引导到职位推荐话题。`,
        {
          isFirstMessage: true,
          sessionContext: session.current_interaction_context,
        }
      );

      return {
        type: "first_ai_response",
        content: aiResponse,
        metadata: {
          isFirstResponse: true,
          needsFollowUp: true,
          responseSource: "ai_inference",
          messageCategory: intent.messageCategory,
        },
        suggestions: [
          "我想了解职位机会",
          "我是技术开发人员",
          "有什么好的推荐吗",
          "我想上传简历",
        ],
      };
    } catch (error) {
      console.error("❌ AI推理第一句回复失败:", error);
      return this.getDefaultFirstResponse();
    }
  }

  /**
   * 获取默认第一次回复
   */
  getDefaultFirstResponse() {
    return {
      type: "first_greeting_default",
      content:
        "您说的我没太明白，这是啥意思？不过没关系，我们合作的公司挺多的，大厂、中厂、创业公司都有，职位也不少",
      metadata: {
        isFirstResponse: true,
        needsFollowUp: true,
        responseSource: "default_first_message",
        followUpMessage: "麻烦告诉我下，你的技术栈、现在公司、职级、期望薪酬？",
      },
      suggestions: [],
    };
  }

  /**
   * 发送开场白消息
   */
  async sendWelcomeMessage(sessionId, userName) {
    try {
      const welcomeText = userName
        ? `您好${userName}，我是AI领域的猎头Katrina，专注于AI算法职位。`
        : `您好，我是AI领域的猎头Katrina，专注于AI算法职位。`;

      await this.database.saveChatMessage({
        sessionId: sessionId,
        messageType: "assistant",
        content: welcomeText,
        metadata: {
          type: "welcome",
          userName: userName,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error("❌ 发送开场白失败:", error);
    }
  }

  /**
   * 获取最近消息
   */
  async getRecentMessages(sessionId, limit = 10) {
    try {
      return await this.database.getSessionMessages(sessionId, limit);
    } catch (error) {
      console.error("❌ 获取最近消息失败:", error);
      return [];
    }
  }

  /**
   * 保存用户消息
   */
  async saveUserMessage(sessionId, message) {
    try {
      return await this.database.saveChatMessage({
        sessionId: sessionId,
        messageType: "user",
        content: message,
        metadata: {
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error("❌ 保存用户消息失败:", error);
    }
  }

  /**
   * 保存助手消息
   */
  async saveAssistantMessage(sessionId, response) {
    try {
      return await this.database.saveChatMessage({
        sessionId: sessionId,
        messageType: "assistant",
        content: typeof response === "string" ? response : response.content,
        metadata: {
          responseType: response.type || "text",
          suggestions: response.suggestions || [],
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error("❌ 保存助手消息失败:", error);
    }
  }

  /**
   * 更新会话上下文
   */
  async updateSessionContext(sessionId, intent, response) {
    try {
      const newContext = {
        lastIntent: intent.type,
        lastResponse: response.type || "text",
        timestamp: new Date().toISOString(),
      };

      await this.database.updateSessionActivity(sessionId, newContext);
    } catch (error) {
      console.error("❌ 更新会话上下文失败:", error);
    }
  }

  /**
   * 获取错误回退响应
   */
  getErrorFallbackResponse() {
    return {
      type: "error_fallback",
      content:
        "抱歉，我遇到了一些技术问题。请稍后再试，或者您可以重新描述您的需求。",
      suggestions: ["重新开始", "联系客服", "查看帮助"],
    };
  }

  /**
   * 处理档案更新（用户提供信息）
   */
  async handleProfileUpdate(intent, messageData, session) {
    try {
      console.log("📝 处理用户档案更新:", messageData.message);

      // 获取用户信息
      const user = await this.userManager.getUserBySession(session);
      if (!user) {
        throw new Error("用户不存在");
      }

      // 1. 解析用户提供的信息（使用新的信息提取服务）
      const extractedInfo = await this.userManager.extractUserInfo(
        messageData.message
      );

      if (!extractedInfo || Object.keys(extractedInfo).length === 0) {
        return {
          type: "no_info_extracted",
          content:
            "我没有从您的消息中识别到具体的信息，能否请您再详细说明一下？",
          metadata: { extractedInfo },
        };
      }

      // 2. 更新用户状态
      const infoState = await this.userManager.updateUserInfoState(
        user.id,
        extractedInfo
      );
      console.log("📊 用户信息状态:", infoState);

      // 3. 检查是否满足推荐条件
      if (infoState.可推荐) {
        console.log("✅ 满足推荐条件，触发职位推荐");
        return await this.activeRecommender.triggerJobRecommendation(
          user.id,
          infoState,
          session,
          this.userManager
        );
      }

      // 4. 如果不满足推荐条件，继续收集信息
      const missingInfo = this.userManager.getMissingInfo(infoState);
      console.log("📋 缺失信息:", missingInfo);

      let response = "好的，我已经记录了您的信息。";

      if (missingInfo.nextToCollect) {
        const nextInfoMap = {
          所在公司: "请问您目前在哪家公司工作？",
          技术方向: "请问您的技术方向是什么？比如推荐算法、NLP、CV等",
          当前职级: "请问您目前的职级是什么？",
          期望薪资: "请问您期望的薪资范围是多少？",
          所在城市: "请问您在哪个城市？",
          业务场景: "请问您希望在什么业务场景工作？比如电商、金融、教育等",
        };

        response += `\n${nextInfoMap[missingInfo.nextToCollect] || "还需要一些其他信息。"}`;
      }

      return {
        type: "profile_updated",
        content: response,
        metadata: {
          extractedInfo,
          infoState,
          missingInfo,
        },
      };
    } catch (error) {
      console.error("❌ 处理档案更新失败:", error);
      return this.getErrorFallbackResponse();
    }
  }

  async handleJobSearch(
    intent,
    messageData,
    session,
    isFirstUserMessage = null
  ) {
    try {
      // 如果没有传入isFirstUserMessage，则检查
      if (isFirstUserMessage === null) {
        isFirstUserMessage = await this.isFirstUserMessage(session.id);
      }

      if (isFirstUserMessage) {
        // 第一句职位询问的特殊处理
        return await this.handleFirstUserGreeting(messageData, session, intent);
      }

      // 非第一句的职位搜索处理，直接使用AI处理

      // 其他职位搜索使用AI处理
      return await this.handleWithAI(intent, messageData, session);
    } catch (error) {
      console.error("❌ 处理职位搜索失败:", error);
      return this.getErrorFallbackResponse();
    }
  }

  async handleRecommendationRequest(intent, messageData, session) {
    try {
      const message = messageData.message.toLowerCase().trim();

      // 第一类：精确匹配 - 纯硬编码
      if (this.isExactJobInquiry(message)) {
        console.log("🎯 精确匹配职位询问，使用硬编码回复");
        return await this.getHardcodedJobInquiryResponse(session);
      }

      // 第二类：AI辅助判断
      const shouldUseHardcoded = await this.shouldUseHardcodedResponse(
        message,
        session
      );
      if (shouldUseHardcoded) {
        console.log("🤖 AI判断为职位询问，使用硬编码回复");
        return await this.getHardcodedJobInquiryResponse(session);
      }

      // 其他情况使用AI处理（仅限信息收集和引导）
      console.log("💬 使用AI处理非职位询问的推荐请求");
      return await this.handleWithAI(intent, messageData, session);
    } catch (error) {
      console.error("❌ 处理推荐请求失败:", error);
      return this.getErrorFallbackResponse();
    }
  }

  async handleTechDirectionInquiry(intent, messageData, session) {
    return await this.handleWithAI(intent, messageData, session);
  }

  async handleSalaryInquiry(intent, messageData, session) {
    return await this.handleWithAI(intent, messageData, session);
  }

  async handleCompanyInquiry(intent, messageData, session) {
    return await this.handleWithAI(intent, messageData, session);
  }

  /**
   * 处理职位详情请求
   */
  async handleJobDetailRequest(intent, messageData, session) {
    try {
      const jobIndex = intent.entities?.jobIndex;

      if (!jobIndex) {
        return {
          type: "error",
          content:
            '抱歉，我没有理解您要查看哪个职位的详情。请回复"详情1"、"详情2"这样的格式。',
        };
      }

      // 从会话上下文中获取最近推荐的职位
      const recentJobs = await this.getRecentRecommendedJobs(session.id);

      if (!recentJobs || recentJobs.length === 0) {
        return {
          type: "no_recent_jobs",
          content: "抱歉，我没有找到最近推荐的职位。请先让我为您推荐一些职位。",
        };
      }

      // 检查索引是否有效
      if (jobIndex < 1 || jobIndex > recentJobs.length) {
        return {
          type: "invalid_index",
          content: `抱歉，职位编号无效。当前可查看的职位编号是1-${recentJobs.length}。`,
        };
      }

      // 获取指定职位的详细信息
      const targetJob = recentJobs[jobIndex - 1];
      const jobDetail = await this.formatJobDetail(targetJob);

      return {
        type: "job_detail",
        content: jobDetail,
        metadata: {
          jobId: targetJob.id,
          jobIndex: jobIndex,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error("❌ 处理职位详情请求失败:", error);
      return {
        type: "error",
        content: "抱歉，获取职位详情时出现问题，请稍后再试。",
      };
    }
  }

  /**
   * 存储推荐的职位到会话上下文
   */
  async storeRecommendedJobs(sessionUuid, jobsList) {
    try {
      // 获取会话ID
      const { data: session, error: sessionError } = await this.database.client
        .from("chat_sessions")
        .select("id")
        .eq("session_uuid", sessionUuid)
        .single();

      if (sessionError) throw sessionError;

      // 更新会话上下文，存储推荐的职位
      const contextUpdate = {
        recommended_jobs: jobsList.map((job, index) => ({
          index: index + 1,
          id: job.id,
          job_title: job.job_title,
          company_name: job.companies?.company_name,
          job_level_raw: job.job_level_raw,
          work_location: job.work_location,
          salary_range: job.salary_range,
          job_description: job.job_description,
          requirements: job.requirements,
          companies: job.companies,
          timestamp: new Date().toISOString(),
        })),
        last_recommendation_time: new Date().toISOString(),
      };

      const { error: updateError } = await this.database.client
        .from("chat_sessions")
        .update({
          current_interaction_context: contextUpdate,
        })
        .eq("id", session.id);

      if (updateError) throw updateError;

      console.log(`✅ 已存储${jobsList.length}个推荐职位到会话上下文`);
    } catch (error) {
      console.error("❌ 存储推荐职位失败:", error);
    }
  }

  /**
   * 获取最近推荐的职位
   */
  async getRecentRecommendedJobs(sessionId) {
    try {
      // 从会话上下文中获取推荐的职位
      const { data: session, error } = await this.database.client
        .from("chat_sessions")
        .select("current_interaction_context")
        .eq("id", sessionId)
        .single();

      if (error) throw error;

      const context = session.current_interaction_context;
      if (context?.recommended_jobs) {
        // 检查推荐时间是否在有效期内（比如1小时内）
        const lastRecommendationTime = new Date(
          context.last_recommendation_time
        );
        const now = new Date();
        const hoursDiff = (now - lastRecommendationTime) / (1000 * 60 * 60);

        if (hoursDiff <= 1) {
          return context.recommended_jobs;
        }
      }

      return [];
    } catch (error) {
      console.error("❌ 获取最近推荐职位失败:", error);
      return [];
    }
  }

  /**
   * 格式化职位详情
   */
  async formatJobDetail(job) {
    try {
      const companyName = job.companies?.company_name || "未知公司";

      let detail = `📋 ${job.job_title || "未知职位"} - ${companyName}\n\n`;

      // 基本信息
      detail += `💼 职级：${job.job_level_raw || `对标P${job.job_standard_level_min || "?"}-P${job.job_standard_level_max || "?"}`}\n`;
      detail += `📍 地点：${job.work_location || "地点待定"}\n`;
      detail += `💰 薪资：${job.salary_range || "面议"}\n\n`;

      // 职位描述
      if (job.job_description) {
        detail += `📝 职位描述：\n${job.job_description}\n\n`;
      }

      // 任职要求
      if (job.requirements) {
        detail += `✅ 任职要求：\n${job.requirements}\n\n`;
      }

      // 公司信息
      if (job.companies) {
        detail += `🏢 公司信息：\n`;
        detail += `• 公司类型：${job.companies.company_type || "未知"}\n`;
        detail += `• 所属行业：${job.companies.industry || "未知"}\n\n`;
      }

      detail += `如果您对这个职位感兴趣，可以告诉我，我来帮您进一步了解或投递简历。`;

      return detail;
    } catch (error) {
      console.error("❌ 格式化职位详情失败:", error);
      return "抱歉，职位详情格式化失败，请稍后再试。";
    }
  }

  async handleUnknownIntent(messageData, session) {
    return {
      type: "unknown_intent",
      content:
        "我不太理解您的意思，能否换个方式描述一下？或者您可以选择以下选项：",
      suggestions: [
        "我想找工作",
        "推荐职位给我",
        "更新个人信息",
        "了解薪资行情",
      ],
    };
  }

  /**
   * 使用AI处理非第一句消息
   */
  async handleWithAI(intent, messageData, session) {
    try {
      console.log(
        "🤖 使用AI处理消息:",
        messageData.message,
        "意图:",
        intent.type
      );

      // 获取用户信息
      const user = await this.userManager.getUserBySession(session);
      if (!user) {
        throw new Error("用户不存在");
      }

      // 1. 先尝试解析用户提供的信息
      const extractedInfo = await this.userManager.extractUserInfo(
        messageData.message
      );
      let infoState = await this.userManager.getUserInfoState(user.id);

      // 2. 如果解析到新信息，更新用户状态
      if (extractedInfo && Object.keys(extractedInfo).length > 0) {
        console.log("📝 解析到用户信息:", extractedInfo);
        infoState = await this.userManager.updateUserInfoState(
          user.id,
          extractedInfo
        );
      }

      // 3. 检查是否满足推荐条件
      if (infoState.可推荐) {
        console.log("✅ 满足推荐条件，触发职位推荐");
        return await this.activeRecommender.triggerJobRecommendation(
          user.id,
          infoState,
          session,
          this.userManager
        );
      }

      // 4. 如果不满足推荐条件，继续收集信息
      const missingInfo = this.userManager.getMissingInfo(infoState);

      // 获取消息历史作为上下文
      const messageHistory = await this.database.getSessionMessages(
        session.id,
        5
      );

      // 获取用户档案信息
      const profile = await this.database.getCandidateProfile(user.id);

      // 构建上下文信息
      let contextPrompt = `用户消息："${messageData.message}"\n`;
      contextPrompt += `意图类型：${intent.type}\n`;

      if (profile) {
        contextPrompt += `用户档案：技术方向=${profile.candidate_tech_direction_raw || "未知"}，经验=${profile.candidate_experience_years || "未知"}年\n`;
      }

      contextPrompt += `请作为AI猎头Katrina专业、友好地回复用户。根据意图类型提供相应的帮助和建议。`;

      const aiResponse = await this.aiServices.generateResponseWithDeepSeek(
        contextPrompt,
        {
          messageHistory: messageHistory,
          sessionContext: session.current_interaction_context,
          userProfile: profile,
          intent: intent,
        }
      );

      return {
        type: "ai_response",
        content: aiResponse,
        metadata: {
          responseSource: "ai_inference",
          intent: intent.type,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error("❌ AI处理消息失败:", error);
      return this.getErrorFallbackResponse();
    }
  }

  // ==================== 信息提取方法 ====================

  /**
   * 从消息中提取公司信息（基于数据库companies表）
   */
  async extractCompanyFromMessage(message) {
    try {
      // 获取数据库中的公司列表
      const { data: companies, error } = await this.database.client
        .from("companies")
        .select("company_name");

      if (error) throw error;

      if (!companies || companies.length === 0) {
        return null;
      }

      const lowerMessage = message.toLowerCase();

      // 查找匹配的公司
      for (const company of companies) {
        const companyName = company.company_name.toLowerCase();
        if (lowerMessage.includes(companyName)) {
          return company.company_name;
        }

        // 检查常见简称
        const aliases = this.getCompanyAliases(company.company_name);
        for (const alias of aliases) {
          if (lowerMessage.includes(alias.toLowerCase())) {
            return company.company_name;
          }
        }
      }

      return null;
    } catch (error) {
      console.error("❌ 提取公司信息失败:", error);
      return null;
    }
  }

  /**
   * 从消息中提取技术栈信息（基于数据库tech_tree表）
   */
  async extractTechFromMessage(message) {
    try {
      // 获取数据库中的技术树
      const { data: techTree, error } = await this.database.client
        .from("tech_tree")
        .select("tech_name, level");

      if (error) throw error;

      if (!techTree || techTree.length === 0) {
        return null;
      }

      const lowerMessage = message.toLowerCase();

      // 查找匹配的技术
      for (const tech of techTree) {
        const techName = tech.tech_name.toLowerCase();
        if (lowerMessage.includes(techName)) {
          return tech.tech_name;
        }
      }

      return null;
    } catch (error) {
      console.error("❌ 提取技术栈信息失败:", error);
      return null;
    }
  }

  /**
   * 从消息中提取职级信息
   */
  extractLevelFromMessage(message) {
    const lowerMessage = message.toLowerCase();

    // 常见职级关键词
    const levelKeywords = {
      p4: "P4",
      p5: "P5",
      p6: "P6",
      p7: "P7",
      p8: "P8",
      p9: "P9",
      t1: "T1",
      t2: "T2",
      t3: "T3",
      t4: "T4",
      t5: "T5",
      t6: "T6",
      "1-1": "1-1",
      "1-2": "1-2",
      "2-1": "2-1",
      "2-2": "2-2",
      "3-1": "3-1",
      初级: "初级",
      中级: "中级",
      高级: "高级",
      专家: "专家",
      架构师: "架构师",
    };

    for (const [keyword, level] of Object.entries(levelKeywords)) {
      if (lowerMessage.includes(keyword)) {
        return level;
      }
    }

    // 检查数字级别（如：10级、11级等）
    const numberLevelMatch = message.match(/(\d+)级/);
    if (numberLevelMatch) {
      return `${numberLevelMatch[1]}级`;
    }

    // 检查腾讯职级（如：T3-1、T3-2等）
    const tencentLevelMatch = message.match(/T(\d+)-(\d+)/i);
    if (tencentLevelMatch) {
      return `T${tencentLevelMatch[1]}-${tencentLevelMatch[2]}`;
    }

    return null;
  }

  /**
   * 从消息中提取薪资信息
   */
  extractSalaryFromMessage(message) {
    // 薪资匹配正则
    const salaryPatterns = [
      /(\d+)k/gi,
      /(\d+)万/gi,
      /(\d+)w/gi, // 130W格式
      /(\d+)-(\d+)k/gi,
      /(\d+)-(\d+)万/gi,
      /(\d+)-(\d+)w/gi,
    ];

    for (const pattern of salaryPatterns) {
      const match = message.match(pattern);
      if (match) {
        return match[0];
      }
    }

    return null;
  }

  /**
   * 获取公司常见别名
   */
  getCompanyAliases(companyName) {
    const aliases = {
      腾讯: ["鹅厂", "tx", "tencent"],
      阿里巴巴: ["阿里", "ali", "alibaba"],
      字节跳动: ["字节", "bd", "bytedance"],
      百度: ["百度", "baidu"],
      美团: ["美团", "meituan"],
      京东: ["京东", "jd"],
      亚马逊: ["亚麻", "amazon"],
      微软: ["微软", "microsoft", "ms"],
      谷歌: ["谷歌", "google"],
    };

    return aliases[companyName] || [];
  }

  // ==================== 用户信息解析和推荐触发 ====================

  /**
   * 解析技术方向
   */
  async extractTechDirection(message) {
    // 基于DATABASE_REFERENCE.md中的技术方向映射
    const techKeywords = {
      多模态: "多模态算法",
      推荐: "推荐算法",
      搜索: "搜索算法",
      CV: "CV算法（计算机视觉）",
      计算机视觉: "CV算法（计算机视觉）",
      图像: "CV算法（计算机视觉）", // 默认映射，可能需要歧义处理
      NLP: "NLP算法（自然语言处理）",
      自然语言: "NLP算法（自然语言处理）",
      大模型: "大模型（LLM）算法",
      LLM: "大模型（LLM）算法",
      机器学习: "通用机器学习/深度学习算法",
      深度学习: "通用机器学习/深度学习算法",
      算法: "通用机器学习/深度学习算法", // 默认映射
    };

    for (const [keyword, techName] of Object.entries(techKeywords)) {
      if (message.includes(keyword)) {
        return techName;
      }
    }

    return null;
  }

  /**
   * 解析业务场景
   */
  async extractBusinessScenario(message) {
    const businessKeywords = {
      电商: "电商零售",
      跨境电商: "电商零售",
      金融: "金融服务",
      银行: "金融服务",
      AI: "人工智能",
      人工智能: "人工智能",
      教育: "教育培训",
      医疗: "医疗健康",
      物流: "物流运输",
      游戏: "娱乐传媒",
    };

    for (const [keyword, scenarioName] of Object.entries(businessKeywords)) {
      if (message.includes(keyword)) {
        return scenarioName;
      }
    }

    return null;
  }

  /**
   * 解析城市信息
   */
  extractCity(message) {
    const cities = [
      "北京",
      "上海",
      "深圳",
      "广州",
      "杭州",
      "成都",
      "武汉",
      "西安",
      "南京",
      "苏州",
      "天津",
      "重庆",
      "青岛",
      "大连",
      "厦门",
      "长沙",
    ];

    for (const city of cities) {
      if (message.includes(city)) {
        return city;
      }
    }

    return null;
  }
}

module.exports = MessageProcessor;
