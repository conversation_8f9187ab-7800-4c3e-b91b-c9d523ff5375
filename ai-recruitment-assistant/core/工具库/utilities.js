/**
 * AI招聘助手系统 - 工具函数库
 *
 * 核心职责：
 * - 通用工具方法
 * - 数据处理函数
 * - 格式化工具
 * - 算法实现
 *
 * 预计代码量：800行
 */

const crypto = require("crypto");
const moment = require("moment");

class Utilities {
  constructor() {
    // 工具类不需要初始化
  }

  // ==================== 字符串处理工具 ====================

  /**
   * 检查字符串是否包含任意一个关键词
   */
  containsAny(text, keywords) {
    if (!text || !Array.isArray(keywords)) return false;

    const lowerText = text.toLowerCase();
    return keywords.some((keyword) =>
      lowerText.includes(keyword.toLowerCase())
    );
  }

  /**
   * 检查字符串是否包含所有关键词
   */
  containsAll(text, keywords) {
    if (!text || !Array.isArray(keywords)) return false;

    const lowerText = text.toLowerCase();
    return keywords.every((keyword) =>
      lowerText.includes(keyword.toLowerCase())
    );
  }

  /**
   * 清理和标准化输入文本
   */
  sanitizeInput(input) {
    if (typeof input !== "string") return "";

    return input
      .trim()
      .replace(/\s+/g, " ") // 多个空格替换为单个空格
      .replace(/[^\w\s\u4e00-\u9fff]/g, "") // 只保留字母、数字、空格和中文
      .substring(0, 1000); // 限制长度
  }

  /**
   * 生成UUID
   */
  generateUUID() {
    return crypto.randomUUID();
  }

  /**
   * 生成随机字符串
   */
  generateRandomString(length = 8) {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 格式化文本为标题格式
   */
  toTitleCase(text) {
    if (!text) return "";

    return text.replace(
      /\w\S*/g,
      (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  }

  // ==================== 数据处理函数 ====================

  /**
   * 深度克隆对象
   */
  deepClone(obj) {
    if (obj === null || typeof obj !== "object") return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map((item) => this.deepClone(item));
    if (typeof obj === "object") {
      const clonedObj = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key]);
        }
      }
      return clonedObj;
    }
  }

  /**
   * 合并对象
   */
  mergeObjects(target, ...sources) {
    if (!target) target = {};

    sources.forEach((source) => {
      if (source && typeof source === "object") {
        Object.keys(source).forEach((key) => {
          if (
            source[key] &&
            typeof source[key] === "object" &&
            !Array.isArray(source[key])
          ) {
            target[key] = this.mergeObjects(target[key] || {}, source[key]);
          } else {
            target[key] = source[key];
          }
        });
      }
    });

    return target;
  }

  /**
   * 验证邮箱格式
   */
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 验证手机号格式
   */
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  /**
   * 数组去重
   */
  uniqueArray(array, key = null) {
    if (!Array.isArray(array)) return [];

    if (key) {
      const seen = new Set();
      return array.filter((item) => {
        const value = item[key];
        if (seen.has(value)) {
          return false;
        }
        seen.add(value);
        return true;
      });
    } else {
      return [...new Set(array)];
    }
  }

  /**
   * 数组分组
   */
  groupBy(array, key) {
    if (!Array.isArray(array)) return {};

    return array.reduce((groups, item) => {
      const group = item[key];
      if (!groups[group]) {
        groups[group] = [];
      }
      groups[group].push(item);
      return groups;
    }, {});
  }

  // ==================== 时间处理工具 ====================

  /**
   * 格式化时间
   */
  formatTime(date, format = "YYYY-MM-DD HH:mm:ss") {
    return moment(date).format(format);
  }

  /**
   * 获取相对时间
   */
  getRelativeTime(date) {
    return moment(date).fromNow();
  }

  /**
   * 检查时间是否在范围内
   */
  isTimeInRange(time, startTime, endTime) {
    const target = moment(time);
    const start = moment(startTime);
    const end = moment(endTime);

    return target.isBetween(start, end, null, "[]");
  }

  /**
   * 获取时间戳
   */
  getTimestamp() {
    return Date.now();
  }

  // ==================== 加密解密函数 ====================

  /**
   * MD5哈希
   */
  md5Hash(text) {
    return crypto.createHash("md5").update(text).digest("hex");
  }

  /**
   * SHA256哈希
   */
  sha256Hash(text) {
    return crypto.createHash("sha256").update(text).digest("hex");
  }

  /**
   * 简单加密
   */
  simpleEncrypt(text, key = "default-key") {
    const cipher = crypto.createCipher("aes192", key);
    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted += cipher.final("hex");
    return encrypted;
  }

  /**
   * 简单解密
   */
  simpleDecrypt(encryptedText, key = "default-key") {
    try {
      const decipher = crypto.createDecipher("aes192", key);
      let decrypted = decipher.update(encryptedText, "hex", "utf8");
      decrypted += decipher.final("utf8");
      return decrypted;
    } catch (error) {
      console.error("解密失败:", error);
      return null;
    }
  }

  /**
   * 生成会话Token（用于链接加密）
   */
  generateSessionToken(email, name = "") {
    const SECRET_KEY =
      process.env.JWT_SECRET || "aiHunter2024_recruitment_system_secret";
    const payload = {
      email: email,
      name: name,
      timestamp: Date.now(),
      nonce: this.generateRandomString(8),
    };

    const payloadString = JSON.stringify(payload);
    return this.simpleEncrypt(payloadString, SECRET_KEY);
  }

  /**
   * 解析会话Token
   */
  parseSessionToken(token) {
    try {
      const SECRET_KEY =
        process.env.JWT_SECRET || "aiHunter2024_recruitment_system_secret";
      const decryptedString = this.simpleDecrypt(token, SECRET_KEY);

      if (!decryptedString) {
        return null;
      }

      const payload = JSON.parse(decryptedString);

      // 检查Token是否过期（14天）
      const fourteenDaysInMs = 14 * 24 * 60 * 60 * 1000;
      if (Date.now() - payload.timestamp > fourteenDaysInMs) {
        return null;
      }

      return {
        email: payload.email,
        name: payload.name,
        timestamp: payload.timestamp,
      };
    } catch (error) {
      console.error("Token解析失败:", error);
      return null;
    }
  }

  // ==================== 算法工具 ====================

  /**
   * 计算字符串相似度（简化版）
   */
  calculateSimilarity(str1, str2) {
    if (!str1 || !str2) return 0;

    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * 计算编辑距离
   */
  levenshteinDistance(str1, str2) {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * 按相关性排序
   */
  sortByRelevance(items, query, keyField = "name") {
    if (!query || !Array.isArray(items)) return items;

    return items
      .map((item) => ({
        ...item,
        _relevance: this.calculateSimilarity(
          query.toLowerCase(),
          (item[keyField] || "").toLowerCase()
        ),
      }))
      .sort((a, b) => b._relevance - a._relevance)
      .map(({ _relevance, ...item }) => item);
  }

  // ==================== 性能优化工具 ====================

  /**
   * 防抖函数
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * 节流函数
   */
  throttle(func, limit) {
    let inThrottle;
    return function (...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  }

  /**
   * 延迟执行
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  // ==================== 数据验证工具 ====================

  /**
   * 检查是否为空值
   */
  isEmpty(value) {
    if (value === null || value === undefined) return true;
    if (typeof value === "string") return value.trim().length === 0;
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === "object") return Object.keys(value).length === 0;
    return false;
  }

  /**
   * 检查是否为有效数字
   */
  isValidNumber(value) {
    return !isNaN(value) && isFinite(value);
  }

  /**
   * 安全的JSON解析
   */
  safeJsonParse(jsonString, defaultValue = null) {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.error("JSON解析失败:", error);
      return defaultValue;
    }
  }

  /**
   * 安全的JSON字符串化
   */
  safeJsonStringify(obj, defaultValue = "{}") {
    try {
      return JSON.stringify(obj);
    } catch (error) {
      console.error("JSON字符串化失败:", error);
      return defaultValue;
    }
  }

  // ==================== 新增工具函数 ====================

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @param {number} decimals - 小数位数
   * @returns {string} 格式化后的文件大小
   */
  formatFileSize(bytes, decimals = 2) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
  }

  /**
   * 截断文本并添加省略号
   * @param {string} text - 原始文本
   * @param {number} maxLength - 最大长度
   * @returns {string} 截断后的文本
   */
  truncateText(text, maxLength = 100) {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + "...";
  }

  /**
   * 生成颜色哈希值（用于头像背景色）
   * @param {string} str - 输入字符串
   * @returns {string} 十六进制颜色值
   */
  generateColorHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }
    const color = Math.abs(hash).toString(16).substring(0, 6);
    return "#" + "000000".substring(0, 6 - color.length) + color;
  }

  /**
   * 计算数组平均值
   * @param {number[]} numbers - 数字数组
   * @returns {number} 平均值
   */
  calculateAverage(numbers) {
    if (!Array.isArray(numbers) || numbers.length === 0) return 0;
    const sum = numbers.reduce((acc, num) => acc + (Number(num) || 0), 0);
    return sum / numbers.length;
  }

  /**
   * 获取数组中位数
   * @param {number[]} numbers - 数字数组
   * @returns {number} 中位数
   */
  calculateMedian(numbers) {
    if (!Array.isArray(numbers) || numbers.length === 0) return 0;
    const sorted = numbers.map((n) => Number(n) || 0).sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 !== 0
      ? sorted[mid]
      : (sorted[mid - 1] + sorted[mid]) / 2;
  }
}

module.exports = Utilities;
