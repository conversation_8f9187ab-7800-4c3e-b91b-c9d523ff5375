/**
 * AI招聘助手系统 - 映射表和常量
 *
 * 核心职责：
 * - 业务常量定义
 * - 映射关系管理
 * - 枚举值维护
 * - 配置数据存储
 *
 * 预计代码量：1200行
 */

class MappingTables {
  constructor() {
    this.initializeMappings();
  }

  /**
   * 初始化所有映射表
   */
  initializeMappings() {
    // 意图类型映射
    this.intentTypes = {
      GREETING: "greeting",
      PROFILE_UPDATE: "profile_update",
      JOB_SEARCH: "job_search",
      RECOMMENDATION_REQUEST: "recommendation_request",
      TECH_DIRECTION_INQUIRY: "tech_direction_inquiry",
      SALARY_INQUIRY: "salary_inquiry",
      COMPANY_INQUIRY: "company_inquiry",
      LOCATION_INQUIRY: "location_inquiry",
      EXPERIENCE_INQUIRY: "experience_inquiry",
      RESUME_UPLOAD: "resume_upload",
      UNKNOWN: "unknown",
    };

    // 技术方向映射 (基于数据库算法技术方向)
    this.techDirectionMapping = {
      // 推荐算法 (ID:725)
      推荐算法: {
        keywords: [
          "推荐",
          "推荐算法",
          "推荐系统",
          "recommendation",
          "个性化推荐",
        ],
        standardName: "推荐算法",
        dbId: 725,
        category: "algorithm",
      },

      // 广告算法 (ID:726)
      广告算法: {
        keywords: ["广告", "广告算法", "广告投放", "ad", "advertising", "竞价"],
        standardName: "广告算法",
        dbId: 726,
        category: "algorithm",
      },

      // 搜索算法 (ID:727)
      搜索算法: {
        keywords: ["搜索", "搜索算法", "search", "检索", "信息检索"],
        standardName: "搜索算法",
        dbId: 727,
        category: "algorithm",
      },

      // CV算法 (ID:728)
      CV算法: {
        keywords: [
          "cv",
          "计算机视觉",
          "图像",
          "视觉",
          "computer vision",
          "图像识别",
        ],
        standardName: "CV算法（计算机视觉）",
        dbId: 728,
        category: "algorithm",
      },

      // NLP算法 (ID:729)
      NLP算法: {
        keywords: [
          "nlp",
          "自然语言处理",
          "文本",
          "语言",
          "natural language",
          "文本分析",
        ],
        standardName: "NLP算法（自然语言处理）",
        dbId: 729,
        category: "algorithm",
      },

      // 多模态算法 (ID:730)
      多模态算法: {
        keywords: ["多模态", "multimodal", "跨模态", "融合算法"],
        standardName: "多模态算法",
        dbId: 730,
        category: "algorithm",
      },

      // 大模型算法 (ID:731)
      大模型算法: {
        keywords: [
          "大模型",
          "llm",
          "大语言模型",
          "gpt",
          "transformer",
          "预训练模型",
        ],
        standardName: "大模型（LLM）算法",
        dbId: 731,
        category: "algorithm",
      },

      // 通用机器学习算法 (ID:734)
      通用机器学习算法: {
        keywords: [
          "机器学习",
          "ml",
          "深度学习",
          "dl",
          "算法",
          "ai",
          "人工智能",
        ],
        standardName: "通用机器学习/深度学习算法",
        dbId: 734,
        category: "algorithm",
      },

      // 语音算法 (ID:732)
      语音算法: {
        keywords: ["语音", "语音识别", "语音合成", "speech", "asr", "tts"],
        standardName: "语音算法",
        dbId: 732,
        category: "algorithm",
      },

      // 视频算法 (ID:733)
      视频算法: {
        keywords: ["视频", "视频理解", "视频分析", "video", "视频处理"],
        standardName: "视频算法",
        dbId: 733,
        category: "algorithm",
      },

      // AIGC生成算法 (ID:738)
      AIGC生成算法: {
        keywords: ["aigc", "生成", "生成算法", "内容生成", "创作"],
        standardName: "AIGC生成算法",
        dbId: 738,
        category: "algorithm",
      },

      // 智能问答算法 (ID:740)
      智能问答算法: {
        keywords: ["问答", "qa", "智能客服", "对话", "chatbot", "agent"],
        standardName: "智能问答/Agent算法",
        dbId: 740,
        category: "algorithm",
      },
    };

    // 公司简称映射（真人猎头式）
    this.companyAliases = {
      // 国内大厂
      腾讯: { cn: "腾讯", alias: "鹅厂", en: "Tencent", short: "TX" },
      阿里巴巴: { cn: "阿里巴巴", alias: "阿里", en: "Alibaba", short: "Ali" },
      字节跳动: { cn: "字节跳动", alias: "字节", en: "ByteDance", short: "BD" },
      百度: { cn: "百度", alias: "百度", en: "Baidu", short: "BD" },
      美团: { cn: "美团", alias: "美团", en: "Meituan", short: "MT" },
      京东: { cn: "京东", alias: "京东", en: "JD", short: "JD" },
      小米: { cn: "小米", alias: "小米", en: "Xiaomi", short: "MI" },
      华为: { cn: "华为", alias: "华为", en: "Huawei", short: "HW" },
      滴滴: { cn: "滴滴", alias: "滴滴", en: "Didi", short: "DD" },
      快手: { cn: "快手", alias: "快手", en: "Kuaishou", short: "KS" },

      // 外资大厂
      微软: { cn: "微软", alias: "微软", en: "Microsoft", short: "MS" },
      谷歌: { cn: "谷歌", alias: "谷歌", en: "Google", short: "G" },
      亚马逊: { cn: "亚马逊", alias: "亚麻", en: "Amazon", short: "AMZ" },
      苹果: { cn: "苹果", alias: "苹果", en: "Apple", short: "AAPL" },
      Meta: { cn: "Meta", alias: "Meta", en: "Meta", short: "META" },
      OpenAI: { cn: "OpenAI", alias: "OpenAI", en: "OpenAI", short: "OAI" },
      Netflix: { cn: "奈飞", alias: "奈飞", en: "Netflix", short: "NFLX" },
      Tesla: { cn: "特斯拉", alias: "特斯拉", en: "Tesla", short: "TSLA" },
      Nvidia: { cn: "英伟达", alias: "英伟达", en: "Nvidia", short: "NVDA" },
      Intel: { cn: "英特尔", alias: "英特尔", en: "Intel", short: "INTC" },
    };

    // 公司类型映射 (基于数据库实际分类)
    this.companyTypeMapping = {
      头部大厂: {
        keywords: [
          "大厂",
          "互联网大厂",
          "bigtech",
          "腾讯",
          "阿里",
          "字节",
          "美团",
          "百度",
        ],
        standardName: "头部大厂",
        description: "知名互联网科技公司",
        characteristics: ["平台大", "福利好", "技术强"],
      },

      中型公司: {
        keywords: ["中型", "中等规模", "知名公司", "上市公司"],
        standardName: "中型公司",
        description: "中等规模的知名企业",
        characteristics: ["发展稳定", "机会较多", "成长空间"],
      },

      国企: {
        keywords: ["国企", "央企", "事业单位", "政府", "国有企业"],
        standardName: "国企",
        description: "国家控股的大型企业",
        characteristics: ["稳定性高", "福利完善", "发展稳健"],
      },

      创业型公司: {
        keywords: ["创业", "初创", "startup", "小公司", "新兴公司"],
        standardName: "创业型公司",
        description: "快速发展的初创企业",
        characteristics: ["成长快", "机会多", "挑战大"],
      },
    };

    // 地理位置映射
    this.locationMapping = {
      beijing: {
        keywords: ["北京", "beijing", "bj"],
        standardName: "北京",
        region: "华北",
        tier: 1,
      },

      shanghai: {
        keywords: ["上海", "shanghai", "sh"],
        standardName: "上海",
        region: "华东",
        tier: 1,
      },

      shenzhen: {
        keywords: ["深圳", "shenzhen", "sz"],
        standardName: "深圳",
        region: "华南",
        tier: 1,
      },

      guangzhou: {
        keywords: ["广州", "guangzhou", "gz"],
        standardName: "广州",
        region: "华南",
        tier: 1,
      },

      hangzhou: {
        keywords: ["杭州", "hangzhou", "hz"],
        standardName: "杭州",
        region: "华东",
        tier: 2,
      },

      nanjing: {
        keywords: ["南京", "nanjing", "nj"],
        standardName: "南京",
        region: "华东",
        tier: 2,
      },

      chengdu: {
        keywords: ["成都", "chengdu", "cd"],
        standardName: "成都",
        region: "西南",
        tier: 2,
      },

      wuhan: {
        keywords: ["武汉", "wuhan", "wh"],
        standardName: "武汉",
        region: "华中",
        tier: 2,
      },
    };

    // 薪资关键词映射
    this.salaryMapping = {
      ranges: {
        junior: {
          min: 8000,
          max: 15000,
          keywords: ["初级", "新手", "应届", "1-3年"],
        },
        middle: {
          min: 15000,
          max: 30000,
          keywords: ["中级", "3-5年", "有经验"],
        },
        senior: { min: 30000, max: 50000, keywords: ["高级", "资深", "5-8年"] },
        expert: {
          min: 50000,
          max: 100000,
          keywords: ["专家", "架构师", "8年以上"],
        },
      },

      keywords: {
        "8k": 8000,
        "10k": 10000,
        "15k": 15000,
        "20k": 20000,
        "25k": 25000,
        "30k": 30000,
        "35k": 35000,
        "40k": 40000,
        "50k": 50000,
        "60k": 60000,
        "80k": 80000,
        "100k": 100000,
      },
    };

    // 经验等级映射
    this.experienceMapping = {
      fresh: {
        keywords: ["应届", "新手", "0年", "无经验", "fresh"],
        standardName: "应届生",
        yearRange: [0, 1],
        level: 1,
      },

      junior: {
        keywords: ["初级", "1年", "2年", "1-3年", "junior"],
        standardName: "初级工程师",
        yearRange: [1, 3],
        level: 2,
      },

      middle: {
        keywords: ["中级", "3年", "4年", "5年", "3-5年", "middle"],
        standardName: "中级工程师",
        yearRange: [3, 5],
        level: 3,
      },

      senior: {
        keywords: ["高级", "资深", "6年", "7年", "8年", "5-8年", "senior"],
        standardName: "高级工程师",
        yearRange: [5, 8],
        level: 4,
      },

      expert: {
        keywords: [
          "专家",
          "架构师",
          "技术专家",
          "8年以上",
          "expert",
          "architect",
        ],
        standardName: "技术专家",
        yearRange: [8, 20],
        level: 5,
      },
    };

    // 职级对照关系
    this.levelMapping = {
      p4: {
        standard: "P4",
        equivalent: ["初级工程师", "T3", "L3"],
        years: "1-3年",
      },
      p5: {
        standard: "P5",
        equivalent: ["中级工程师", "T4", "L4"],
        years: "3-5年",
      },
      p6: {
        standard: "P6",
        equivalent: ["高级工程师", "T5", "L5"],
        years: "5-8年",
      },
      p7: {
        standard: "P7",
        equivalent: ["资深工程师", "T6", "L6"],
        years: "8-10年",
      },
      p8: {
        standard: "P8",
        equivalent: ["技术专家", "T7", "L7"],
        years: "10年以上",
      },
    };

    // 业务场景映射 (基于数据库实际业务场景)
    this.businessScenarioMapping = {
      // 电商零售 (ID:2000)
      电商零售: {
        keywords: [
          "电商",
          "零售",
          "网购",
          "购物",
          "商城",
          "淘宝",
          "京东",
          "拼多多",
        ],
        standardName: "电商零售",
        dbId: 2000,
        category: "business",
      },

      // 金融服务 (ID:2001)
      金融服务: {
        keywords: [
          "金融",
          "银行",
          "保险",
          "投资",
          "理财",
          "贷款",
          "支付",
          "证券",
        ],
        standardName: "金融服务",
        dbId: 2001,
        category: "business",
      },

      // 人工智能 (ID:2002)
      人工智能: {
        keywords: [
          "AI",
          "人工智能",
          "机器学习",
          "深度学习",
          "算法",
          "智能",
          "自动化",
        ],
        standardName: "人工智能",
        dbId: 2002,
        category: "business",
      },

      // 教育培训 (ID:2003)
      教育培训: {
        keywords: [
          "教育",
          "培训",
          "学习",
          "课程",
          "在线教育",
          "知识付费",
          "技能培训",
        ],
        standardName: "教育培训",
        dbId: 2003,
        category: "business",
      },

      // 娱乐传媒 (ID:2009)
      娱乐传媒: {
        keywords: [
          "娱乐",
          "传媒",
          "游戏",
          "视频",
          "音乐",
          "直播",
          "社交",
          "内容",
        ],
        standardName: "娱乐传媒",
        dbId: 2009,
        category: "business",
      },
    };

    // 固定话术库
    this.fixedResponses = {
      greeting: {
        newUser: "你好！欢迎使用AI招聘助手。我是Katrina，很高兴为您服务！",
        returningUser: "欢迎回来！今天想要什么帮助吗？",
        general: "您好！我可以帮您找到合适的工作机会。",
      },

      profileIncomplete: {
        techDirection: "为了给您推荐最合适的职位，请告诉我您的技术方向？",
        experience: "请告诉我您有多少年工作经验？",
        salary: "您期望的薪资范围是多少？",
        location: "您希望在哪个城市工作？",
      },

      clarification: {
        techDirection: "您提到的技术方向我不太确定，您是指以下哪一种？",
        ambiguous: "您的描述有些模糊，能否更具体一些？",
        multiple: "我找到了多个相关选项，请选择最符合您需求的：",
      },

      error: {
        general: "抱歉，我遇到了一些技术问题。请稍后再试。",
        noResults: "抱歉，没有找到符合条件的职位。您可以调整一下搜索条件。",
        invalidInput: "您的输入格式不正确，请重新输入。",
      },
    };

    // 推荐策略配置
    this.recommendationConfig = {
      matrix4x4: {
        companyTypes: ["startup", "bigtech", "traditional", "foreign"],
        techDirections: ["frontend", "backend", "fullstack", "mobile"],
        maxJobsPerCell: 3,
        minMatchScore: 0.6,
      },

      scoring: {
        techMatch: 0.4, // 技术匹配权重
        salaryMatch: 0.3, // 薪资匹配权重
        locationMatch: 0.2, // 地点匹配权重
        experienceMatch: 0.1, // 经验匹配权重
      },

      filters: {
        maxRecommendations: 20,
        diversityFactor: 0.3,
        freshnessDays: 30,
      },
    };
  }

  // ==================== 获取方法 ====================

  /**
   * 获取意图类型
   */
  getIntentTypes() {
    return this.intentTypes;
  }

  /**
   * 获取技术方向映射
   */
  getTechDirectionMapping() {
    return this.techDirectionMapping;
  }

  /**
   * 获取公司类型映射
   */
  getCompanyTypeMapping() {
    return this.companyTypeMapping;
  }

  /**
   * 获取地理位置映射
   */
  getLocationMapping() {
    return this.locationMapping;
  }

  /**
   * 获取薪资映射
   */
  getSalaryMapping() {
    return this.salaryMapping;
  }

  /**
   * 获取经验映射
   */
  getExperienceMapping() {
    return this.experienceMapping;
  }

  /**
   * 获取业务场景映射
   */
  getBusinessScenarioMapping() {
    return this.businessScenarioMapping;
  }

  /**
   * 获取固定话术
   */
  getFixedResponses() {
    return this.fixedResponses;
  }

  /**
   * 获取推荐配置
   */
  getRecommendationConfig() {
    return this.recommendationConfig;
  }

  // ==================== 查找方法 ====================

  /**
   * 根据关键词查找技术方向
   */
  findTechDirectionByKeyword(keyword) {
    const lowerKeyword = keyword.toLowerCase();

    for (const [key, mapping] of Object.entries(this.techDirectionMapping)) {
      if (
        mapping.keywords.some((k) => k.toLowerCase().includes(lowerKeyword))
      ) {
        return { key, ...mapping };
      }
    }

    return null;
  }

  /**
   * 根据关键词查找公司类型
   */
  findCompanyTypeByKeyword(keyword) {
    const lowerKeyword = keyword.toLowerCase();

    for (const [key, mapping] of Object.entries(this.companyTypeMapping)) {
      if (
        mapping.keywords.some((k) => k.toLowerCase().includes(lowerKeyword))
      ) {
        return { key, ...mapping };
      }
    }

    return null;
  }

  /**
   * 根据关键词查找地理位置
   */
  findLocationByKeyword(keyword) {
    const lowerKeyword = keyword.toLowerCase();

    for (const [key, mapping] of Object.entries(this.locationMapping)) {
      if (
        mapping.keywords.some((k) => k.toLowerCase().includes(lowerKeyword))
      ) {
        return { key, ...mapping };
      }
    }

    return null;
  }

  /**
   * 根据年限查找经验等级
   */
  findExperienceByYears(years) {
    for (const [key, mapping] of Object.entries(this.experienceMapping)) {
      if (years >= mapping.yearRange[0] && years <= mapping.yearRange[1]) {
        return { key, ...mapping };
      }
    }

    return null;
  }

  /**
   * 解析薪资关键词
   */
  parseSalaryKeyword(keyword) {
    const lowerKeyword = keyword.toLowerCase();

    // 直接匹配
    if (this.salaryMapping.keywords[lowerKeyword]) {
      return this.salaryMapping.keywords[lowerKeyword];
    }

    // 数字提取
    const numberMatch = keyword.match(/(\d+)k?/i);
    if (numberMatch) {
      const number = parseInt(numberMatch[1]);
      return keyword.toLowerCase().includes("k") ? number * 1000 : number;
    }

    return null;
  }
}

module.exports = MappingTables;
