/**
 * AI招聘助手系统 - 映射表和常量
 *
 * 核心职责：
 * - 业务常量定义
 * - 映射关系管理
 * - 枚举值维护
 * - 配置数据存储
 *
 * 预计代码量：1200行
 */

class MappingTables {
  constructor() {
    this.initializeMappings();
  }

  /**
   * 初始化所有映射表
   */
  initializeMappings() {
    // 意图类型映射
    this.intentTypes = {
      GREETING: "greeting",
      PROFILE_UPDATE: "profile_update",
      JOB_SEARCH: "job_search",
      RECOMMENDATION_REQUEST: "recommendation_request",
      TECH_DIRECTION_INQUIRY: "tech_direction_inquiry",
      SALARY_INQUIRY: "salary_inquiry",
      COMPANY_INQUIRY: "company_inquiry",
      LOCATION_INQUIRY: "location_inquiry",
      EXPERIENCE_INQUIRY: "experience_inquiry",
      RESUME_UPLOAD: "resume_upload",
      UNKNOWN: "unknown",
    };

    // 技术方向映射 (基于数据库算法技术方向)
    this.techDirectionMapping = {
      // 推荐算法 (ID:725)
      推荐算法: {
        keywords: [
          "推荐",
          "推荐算法",
          "推荐系统",
          "recommendation",
          "个性化推荐",
        ],
        standardName: "推荐算法",
        dbId: 725,
        category: "algorithm",
      },

      // 广告算法 (ID:726)
      广告算法: {
        keywords: ["广告", "广告算法", "广告投放", "ad", "advertising", "竞价"],
        standardName: "广告算法",
        dbId: 726,
        category: "algorithm",
      },

      // 搜索算法 (ID:727)
      搜索算法: {
        keywords: ["搜索", "搜索算法", "search", "检索", "信息检索"],
        standardName: "搜索算法",
        dbId: 727,
        category: "algorithm",
      },

      // CV算法 (ID:728)
      CV算法: {
        keywords: [
          "cv",
          "计算机视觉",
          "图像",
          "视觉",
          "computer vision",
          "图像识别",
        ],
        standardName: "CV算法（计算机视觉）",
        dbId: 728,
        category: "algorithm",
      },

      // NLP算法 (ID:729)
      NLP算法: {
        keywords: [
          "nlp",
          "自然语言处理",
          "文本",
          "语言",
          "natural language",
          "文本分析",
        ],
        standardName: "NLP算法（自然语言处理）",
        dbId: 729,
        category: "algorithm",
      },

      // 多模态算法 (ID:730)
      多模态算法: {
        keywords: ["多模态", "multimodal", "跨模态", "融合算法"],
        standardName: "多模态算法",
        dbId: 730,
        category: "algorithm",
      },

      // 大模型算法 (ID:731)
      大模型算法: {
        keywords: [
          "大模型",
          "llm",
          "大语言模型",
          "gpt",
          "transformer",
          "预训练模型",
        ],
        standardName: "大模型（LLM）算法",
        dbId: 731,
        category: "algorithm",
      },

      // 通用机器学习算法 (ID:734)
      通用机器学习算法: {
        keywords: [
          "机器学习",
          "ml",
          "深度学习",
          "dl",
          "算法",
          "ai",
          "人工智能",
        ],
        standardName: "通用机器学习/深度学习算法",
        dbId: 734,
        category: "algorithm",
      },

      // 语音算法 (ID:732)
      语音算法: {
        keywords: ["语音", "语音识别", "语音合成", "speech", "asr", "tts"],
        standardName: "语音算法",
        dbId: 732,
        category: "algorithm",
      },

      // 视频算法 (ID:733)
      视频算法: {
        keywords: ["视频", "视频理解", "视频分析", "video", "视频处理"],
        standardName: "视频算法",
        dbId: 733,
        category: "algorithm",
      },

      // AIGC生成算法 (ID:738)
      AIGC生成算法: {
        keywords: ["aigc", "生成", "生成算法", "内容生成", "创作"],
        standardName: "AIGC生成算法",
        dbId: 738,
        category: "algorithm",
      },

      // 智能问答算法 (ID:740)
      智能问答算法: {
        keywords: ["问答", "qa", "智能客服", "对话", "chatbot", "agent"],
        standardName: "智能问答/Agent算法",
        dbId: 740,
        category: "algorithm",
      },
    };

    // 公司类型查询规则（动态数据通过数据库获取）

    // 公司类型映射 (查询规则，具体公司数据来自数据库)
    this.companyTypeMapping = {
      头部大厂: {
        keywords: ["大厂", "互联网大厂", "bigtech", "头部公司", "知名大厂"],
        standardName: "头部大厂",
        queryCondition: "company_type = '头部大厂'",
        description: "知名互联网科技公司",
        characteristics: ["平台大", "福利好", "技术强"],
      },

      中型公司: {
        keywords: ["中型", "中等规模", "知名公司", "上市公司", "中型企业"],
        standardName: "中型公司",
        queryCondition: "company_type = '中型公司'",
        description: "中等规模的知名企业",
        characteristics: ["发展稳定", "机会较多", "成长空间"],
      },

      国企: {
        keywords: ["国企", "央企", "事业单位", "政府", "国有企业", "国有公司"],
        standardName: "国企",
        queryCondition: "company_type = '国企'",
        description: "国家控股的大型企业",
        characteristics: ["稳定性高", "福利完善", "发展稳健"],
      },

      创业型公司: {
        keywords: ["创业", "初创", "startup", "小公司", "新兴公司", "创业公司"],
        standardName: "创业型公司",
        queryCondition: "company_type = '创业型公司'",
        description: "快速发展的初创企业",
        characteristics: ["成长快", "机会多", "挑战大"],
      },
    };

    // 地理位置映射
    this.locationMapping = {
      beijing: {
        keywords: ["北京", "beijing", "bj"],
        standardName: "北京",
        region: "华北",
        tier: 1,
      },

      shanghai: {
        keywords: ["上海", "shanghai", "sh"],
        standardName: "上海",
        region: "华东",
        tier: 1,
      },

      shenzhen: {
        keywords: ["深圳", "shenzhen", "sz"],
        standardName: "深圳",
        region: "华南",
        tier: 1,
      },

      guangzhou: {
        keywords: ["广州", "guangzhou", "gz"],
        standardName: "广州",
        region: "华南",
        tier: 1,
      },

      hangzhou: {
        keywords: ["杭州", "hangzhou", "hz"],
        standardName: "杭州",
        region: "华东",
        tier: 2,
      },

      nanjing: {
        keywords: ["南京", "nanjing", "nj"],
        standardName: "南京",
        region: "华东",
        tier: 2,
      },

      chengdu: {
        keywords: ["成都", "chengdu", "cd"],
        standardName: "成都",
        region: "西南",
        tier: 2,
      },

      wuhan: {
        keywords: ["武汉", "wuhan", "wh"],
        standardName: "武汉",
        region: "华中",
        tier: 2,
      },
    };

    // 薪资关键词映射
    this.salaryMapping = {
      ranges: {
        junior: {
          min: 8000,
          max: 15000,
          keywords: ["初级", "新手", "应届", "1-3年"],
        },
        middle: {
          min: 15000,
          max: 30000,
          keywords: ["中级", "3-5年", "有经验"],
        },
        senior: { min: 30000, max: 50000, keywords: ["高级", "资深", "5-8年"] },
        expert: {
          min: 50000,
          max: 100000,
          keywords: ["专家", "架构师", "8年以上"],
        },
      },

      keywords: {
        "8k": 8000,
        "10k": 10000,
        "15k": 15000,
        "20k": 20000,
        "25k": 25000,
        "30k": 30000,
        "35k": 35000,
        "40k": 40000,
        "50k": 50000,
        "60k": 60000,
        "80k": 80000,
        "100k": 100000,
      },
    };

    // 经验等级映射
    this.experienceMapping = {
      fresh: {
        keywords: ["应届", "新手", "0年", "无经验", "fresh"],
        standardName: "应届生",
        yearRange: [0, 1],
        level: 1,
      },

      junior: {
        keywords: ["初级", "1年", "2年", "1-3年", "junior"],
        standardName: "初级工程师",
        yearRange: [1, 3],
        level: 2,
      },

      middle: {
        keywords: ["中级", "3年", "4年", "5年", "3-5年", "middle"],
        standardName: "中级工程师",
        yearRange: [3, 5],
        level: 3,
      },

      senior: {
        keywords: ["高级", "资深", "6年", "7年", "8年", "5-8年", "senior"],
        standardName: "高级工程师",
        yearRange: [5, 8],
        level: 4,
      },

      expert: {
        keywords: [
          "专家",
          "架构师",
          "技术专家",
          "8年以上",
          "expert",
          "architect",
        ],
        standardName: "技术专家",
        yearRange: [8, 20],
        level: 5,
      },
    };

    // 职级对照关系 (基于job_listings表实际数据)
    this.levelMapping = {
      // 阿里系职级 (P6-P9)
      P6: {
        standard: "P6",
        equivalent: ["高级工程师"],
        years: "4-7年",
        salary: "100-150万",
      },
      P7: {
        standard: "P7",
        equivalent: ["专家工程师"],
        years: "6-9年",
        salary: "100-250万",
      },
      P8: {
        standard: "P8",
        equivalent: ["资深专家"],
        years: "8-11年",
        salary: "100-250万",
      },
      P9: {
        standard: "P9",
        equivalent: ["首席专家"],
        years: "10-13年",
        salary: "50-250万",
      },

      // 滴滴系职级 (D8-D9)
      D8: {
        standard: "P8",
        equivalent: ["滴滴D8"],
        years: "8-11年",
        salary: "100-150万",
      },
      D9: {
        standard: "P9",
        equivalent: ["滴滴D9"],
        years: "10-13年",
        salary: "100-250万",
      },

      // 美团系职级 (L8-L10)
      L8: {
        standard: "P8",
        equivalent: ["美团L8"],
        years: "8-11年",
        salary: "100-400万",
      },
      L9: {
        standard: "P9",
        equivalent: ["美团L9"],
        years: "10-13年",
        salary: "100-400万",
      },
      L10: {
        standard: "P10",
        equivalent: ["美团L10"],
        years: "12-15年",
        salary: "100-400万",
      },

      // 小红书系职级 (R5-R6)
      R5: {
        standard: "P6",
        equivalent: ["小红书R5"],
        years: "4-7年",
        salary: "50-150万",
      },
      R6: {
        standard: "P7",
        equivalent: ["小红书R6"],
        years: "6-9年",
        salary: "100-150万",
      },

      // 其他职级格式
      "E5-E7": {
        standard: "P6",
        equivalent: ["E5-E7"],
        years: "4-8年",
        salary: "50-150万",
      },
      无: {
        standard: "通用",
        equivalent: ["未标注"],
        years: "不限",
        salary: "30-400万",
      },
    };

    // 业务场景映射 (基于数据库实际业务场景)
    this.businessScenarioMapping = {
      // 电商零售 (ID:2000)
      电商零售: {
        keywords: [
          "电商",
          "零售",
          "网购",
          "购物",
          "商城",
          "淘宝",
          "京东",
          "拼多多",
        ],
        standardName: "电商零售",
        dbId: 2000,
        category: "business",
      },

      // 金融服务 (ID:2001)
      金融服务: {
        keywords: [
          "金融",
          "银行",
          "保险",
          "投资",
          "理财",
          "贷款",
          "支付",
          "证券",
        ],
        standardName: "金融服务",
        dbId: 2001,
        category: "business",
      },

      // 人工智能 (ID:2002)
      人工智能: {
        keywords: [
          "AI",
          "人工智能",
          "机器学习",
          "深度学习",
          "算法",
          "智能",
          "自动化",
        ],
        standardName: "人工智能",
        dbId: 2002,
        category: "business",
      },

      // 教育培训 (ID:2003)
      教育培训: {
        keywords: [
          "教育",
          "培训",
          "学习",
          "课程",
          "在线教育",
          "知识付费",
          "技能培训",
        ],
        standardName: "教育培训",
        dbId: 2003,
        category: "business",
      },

      // 娱乐传媒 (ID:2009)
      娱乐传媒: {
        keywords: [
          "娱乐",
          "传媒",
          "游戏",
          "视频",
          "音乐",
          "直播",
          "社交",
          "内容",
          "媒体",
        ],
        standardName: "娱乐传媒",
        dbId: 2009,
        category: "business",
      },

      // 医疗健康 (ID:2004)
      医疗健康: {
        keywords: [
          "医疗",
          "健康",
          "医院",
          "医生",
          "药品",
          "诊断",
          "治疗",
          "体检",
          "养生",
          "保健",
        ],
        standardName: "医疗健康",
        dbId: 2004,
        category: "business",
      },

      // 物流运输 (ID:2005)
      物流运输: {
        keywords: [
          "物流",
          "运输",
          "快递",
          "配送",
          "仓储",
          "供应链",
          "货运",
          "邮寄",
          "发货",
        ],
        standardName: "物流运输",
        dbId: 2005,
        category: "business",
      },

      // 房地产 (ID:2006)
      房地产: {
        keywords: [
          "房地产",
          "房产",
          "买房",
          "租房",
          "装修",
          "建筑",
          "地产",
          "楼盘",
          "中介",
        ],
        standardName: "房地产",
        dbId: 2006,
        category: "business",
      },

      // 旅游出行 (ID:2007)
      旅游出行: {
        keywords: [
          "旅游",
          "出行",
          "酒店",
          "机票",
          "景点",
          "度假",
          "民宿",
          "自驾",
          "攻略",
        ],
        standardName: "旅游出行",
        dbId: 2007,
        category: "business",
      },

      // 餐饮美食 (ID:2008)
      餐饮美食: {
        keywords: [
          "餐饮",
          "美食",
          "外卖",
          "餐厅",
          "菜谱",
          "烹饪",
          "食材",
          "饮品",
          "小吃",
        ],
        standardName: "餐饮美食",
        dbId: 2008,
        category: "business",
      },

      // 企业服务 (ID:2010)
      企业服务: {
        keywords: [
          "企业服务",
          "SaaS",
          "办公",
          "管理",
          "协作",
          "云服务",
          "CRM",
          "ERP",
          "工具",
        ],
        standardName: "企业服务",
        dbId: 2010,
        category: "business",
      },

      // 制造业 (ID:2011)
      制造业: {
        keywords: [
          "制造",
          "生产",
          "工厂",
          "加工",
          "制造业",
          "工业",
          "设备",
          "自动化",
          "质量控制",
        ],
        standardName: "制造业",
        dbId: 2011,
        category: "business",
      },

      // 农业 (ID:2012)
      农业: {
        keywords: [
          "农业",
          "种植",
          "养殖",
          "农产品",
          "农场",
          "农民",
          "农村",
          "绿色",
          "有机",
        ],
        standardName: "农业",
        dbId: 2012,
        category: "business",
      },

      // 能源环保 (ID:2013)
      能源环保: {
        keywords: [
          "能源",
          "环保",
          "新能源",
          "节能",
          "绿色",
          "清洁",
          "可再生",
          "环境",
          "碳中和",
        ],
        standardName: "能源环保",
        dbId: 2013,
        category: "business",
      },

      // 政府公共 (ID:2014)
      政府公共: {
        keywords: [
          "政府",
          "公共服务",
          "政务",
          "民生",
          "社会",
          "公益",
          "便民",
          "数字政府",
        ],
        standardName: "政府公共",
        dbId: 2014,
        category: "business",
      },
    };

    // 固定话术库
    this.fixedResponses = {
      greeting: {
        newUser: "你好！欢迎使用AI招聘助手。我是Katrina，很高兴为您服务！",
        returningUser: "欢迎回来！今天想要什么帮助吗？",
        general: "您好！我可以帮您找到合适的工作机会。",
      },

      profileIncomplete: {
        techDirection: "为了给您推荐最合适的职位，请告诉我您的技术方向？",
        experience: "请告诉我您有多少年工作经验？",
        salary: "您期望的薪资范围是多少？",
        location: "您希望在哪个城市工作？",
      },

      clarification: {
        techDirection: "您提到的技术方向我不太确定，您是指以下哪一种？",
        ambiguous: "您的描述有些模糊，能否更具体一些？",
        multiple: "我找到了多个相关选项，请选择最符合您需求的：",
      },

      error: {
        general: "抱歉，我遇到了一些技术问题。请稍后再试。",
        noResults: "抱歉，没有找到符合条件的职位。您可以调整一下搜索条件。",
        invalidInput: "您的输入格式不正确，请重新输入。",
      },
    };

    // 推荐策略配置
    this.recommendationConfig = {
      matrix4x4: {
        companyTypes: ["startup", "bigtech", "traditional", "foreign"],
        techDirections: ["frontend", "backend", "fullstack", "mobile"],
        maxJobsPerCell: 3,
        minMatchScore: 0.6,
      },

      scoring: {
        techMatch: 0.4, // 技术匹配权重
        salaryMatch: 0.3, // 薪资匹配权重
        locationMatch: 0.2, // 地点匹配权重
        experienceMatch: 0.1, // 经验匹配权重
      },

      filters: {
        maxRecommendations: 20,
        diversityFactor: 0.3,
        freshnessDays: 30,
      },
    };
  }

  // ==================== 获取方法 ====================

  /**
   * 获取意图类型
   */
  getIntentTypes() {
    return this.intentTypes;
  }

  /**
   * 获取技术方向映射
   */
  getTechDirectionMapping() {
    return this.techDirectionMapping;
  }

  /**
   * 获取公司类型映射
   */
  getCompanyTypeMapping() {
    return this.companyTypeMapping;
  }

  /**
   * 获取地理位置映射
   */
  getLocationMapping() {
    return this.locationMapping;
  }

  /**
   * 获取薪资映射
   */
  getSalaryMapping() {
    return this.salaryMapping;
  }

  /**
   * 获取经验映射
   */
  getExperienceMapping() {
    return this.experienceMapping;
  }

  /**
   * 获取业务场景映射
   */
  getBusinessScenarioMapping() {
    return this.businessScenarioMapping;
  }

  /**
   * 获取固定话术
   */
  getFixedResponses() {
    return this.fixedResponses;
  }

  /**
   * 获取推荐配置
   */
  getRecommendationConfig() {
    return this.recommendationConfig;
  }

  // ==================== 查找方法 ====================

  /**
   * 根据关键词查找技术方向
   */
  findTechDirectionByKeyword(keyword) {
    const lowerKeyword = keyword.toLowerCase();

    for (const [key, mapping] of Object.entries(this.techDirectionMapping)) {
      if (
        mapping.keywords.some((k) => k.toLowerCase().includes(lowerKeyword))
      ) {
        return { key, ...mapping };
      }
    }

    return null;
  }

  /**
   * 根据关键词查找公司类型
   */
  findCompanyTypeByKeyword(keyword) {
    const lowerKeyword = keyword.toLowerCase();

    for (const [key, mapping] of Object.entries(this.companyTypeMapping)) {
      if (
        mapping.keywords.some((k) => k.toLowerCase().includes(lowerKeyword))
      ) {
        return { key, ...mapping };
      }
    }

    return null;
  }

  /**
   * 根据关键词查找地理位置
   */
  findLocationByKeyword(keyword) {
    const lowerKeyword = keyword.toLowerCase();

    for (const [key, mapping] of Object.entries(this.locationMapping)) {
      if (
        mapping.keywords.some((k) => k.toLowerCase().includes(lowerKeyword))
      ) {
        return { key, ...mapping };
      }
    }

    return null;
  }

  /**
   * 根据年限查找经验等级
   */
  findExperienceByYears(years) {
    for (const [key, mapping] of Object.entries(this.experienceMapping)) {
      if (years >= mapping.yearRange[0] && years <= mapping.yearRange[1]) {
        return { key, ...mapping };
      }
    }

    return null;
  }

  /**
   * 解析薪资关键词
   */
  parseSalaryKeyword(keyword) {
    const lowerKeyword = keyword.toLowerCase();

    // 直接匹配
    if (this.salaryMapping.keywords[lowerKeyword]) {
      return this.salaryMapping.keywords[lowerKeyword];
    }

    // 数字提取
    const numberMatch = keyword.match(/(\d+)k?/i);
    if (numberMatch) {
      const number = parseInt(numberMatch[1]);
      return keyword.toLowerCase().includes("k") ? number * 1000 : number;
    }

    return null;
  }

  // ==================== 动态查询方法 ====================

  /**
   * 根据公司类型获取数据库查询条件
   * @param {string} companyType - 公司类型
   * @returns {string|null} 数据库查询条件
   */
  getCompanyQueryCondition(companyType) {
    return this.companyTypeMapping[companyType]?.queryCondition || null;
  }

  /**
   * 根据技术方向获取数据库ID
   * @param {string} techDirection - 技术方向
   * @returns {number|null} 数据库ID
   */
  getTechDirectionId(techDirection) {
    return this.techDirectionMapping[techDirection]?.dbId || null;
  }

  /**
   * 根据业务场景获取数据库ID
   * @param {string} businessScenario - 业务场景
   * @returns {number|null} 数据库ID
   */
  getBusinessScenarioId(businessScenario) {
    return this.businessScenarioMapping[businessScenario]?.dbId || null;
  }

  /**
   * 识别用户输入的公司类型
   * @param {string} input - 用户输入
   * @returns {string|null} 匹配的公司类型
   */
  identifyCompanyType(input) {
    const lowerInput = input.toLowerCase();

    for (const [type, mapping] of Object.entries(this.companyTypeMapping)) {
      if (
        mapping.keywords.some((keyword) =>
          lowerInput.includes(keyword.toLowerCase())
        )
      ) {
        return type;
      }
    }

    return null;
  }
}

module.exports = MappingTables;
