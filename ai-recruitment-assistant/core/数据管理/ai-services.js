/**
 * AI招聘助手系统 - AI服务模块
 *
 * 核心职责：
 * - DeepSeek API 集成
 * - 智能分析和生成
 * - 上下文理解
 * - 信息提取
 *
 * 预计代码量：1600行
 */

const axios = require("axios");

class AIServices {
  constructor(config) {
    this.config = config;
    this.client = null;
    this.isInitialized = false;

    // API配置
    this.apiEndpoint = config.deepseekEndpoint;
    this.apiKey = config.deepseekApiKey;
    this.maxTokens = config.maxTokens;
    this.temperature = config.temperature;
    this.timeout = config.timeout;

    // 高并发优化配置
    this.requestQueue = [];
    this.processing = false;
    this.maxRetries = 3;
    this.rateLimitDelay = 1000;
    this.maxConcurrentRequests = 5;
    this.currentRequests = 0;

    // 请求统计
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      rateLimitHits: 0,
    };

    // Qwen模型配置
    this.qwenConfig = {
      apiKey: config.qwenApiKey,
      endpoint: config.qwenEndpoint,
      model: config.qwenModel,
      timeout: config.timeout || 30000,
      maxRetries: 3,
    };

    // 双模型统计数据
    this.modelStats = {
      qwen: {
        requests: 0,
        failures: 0,
        totalResponseTime: 0,
        averageResponseTime: 0,
        successRate: 0,
      },
      deepseek: {
        requests: 0,
        failures: 0,
        totalResponseTime: 0,
        averageResponseTime: 0,
        successRate: 0,
      },
    };

    // 模型角色分工配置
    this.modelRoles = {
      qwen: {
        primary: ["intent_analysis", "info_extraction", "user_analysis"],
        description: "Qwen负责意图识别、信息提取、用户分析",
      },
      deepseek: {
        primary: ["response_generation", "conversation_inference"],
        description: "DeepSeek负责对话生成、回复推理",
      },
    };

    // 模型调用队列和并发控制
    this.requestQueues = {
      qwen: [],
      deepseek: [],
    };
    this.concurrencyLimits = {
      qwen: 3,
      deepseek: 3,
      total: 5,
    };
    this.activeRequests = {
      qwen: 0,
      deepseek: 0,
      total: 0,
    };
  }

  /**
   * 初始化AI服务
   */
  async initialize() {
    try {
      // 配置HTTP客户端
      this.client = axios.create({
        baseURL: "https://api.deepseek.com/v1",
        timeout: this.timeout,
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
        },
      });

      // 在测试环境下跳过连接测试
      if (this.apiKey && this.apiKey !== "test-key") {
        await this.testConnection();
      } else {
        console.log("🧪 测试环境：跳过AI服务连接测试");
      }

      this.isInitialized = true;
      console.log("🤖 AI服务初始化完成");
    } catch (error) {
      console.error("❌ AI服务初始化失败:", error);
      // 在测试环境下不抛出错误
      if (this.apiKey === "test-key") {
        console.log("🧪 测试环境：忽略初始化错误");
        this.isInitialized = true;
      } else {
        throw error;
      }
    }
  }

  /**
   * 测试AI服务连接
   */
  async testConnection() {
    try {
      const response = await this.client.post("/chat/completions", {
        model: "deepseek-chat",
        messages: [{ role: "user", content: "Hello" }],
        max_tokens: 10,
      });

      if (response.status === 200) {
        console.log("✅ AI服务连接测试成功");
      }
    } catch (error) {
      console.error("❌ AI服务连接测试失败:", error);
      throw error;
    }
  }

  /**
   * 分析用户意图（带队列处理）
   */
  async analyzeUserIntent(message, context = {}) {
    return this.queueRequest(
      async () => {
        const prompt = this.buildIntentAnalysisPrompt(message, context);

        const response = await this.makeAPIRequest("/chat/completions", {
          model: "deepseek-chat",
          messages: [
            { role: "system", content: this.getIntentAnalysisSystemPrompt() },
            { role: "user", content: prompt },
          ],
          max_tokens: 500,
          temperature: 0.3,
        });

        const result = response.data.choices[0].message.content;
        return this.parseIntentAnalysisResult(result);
      },
      {
        fallback: {
          type: "unknown",
          confidence: 0.1,
          entities: {},
        },
      }
    );
  }

  /**
   * 请求队列处理
   */
  async queueRequest(requestFn, options = {}) {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        requestFn,
        resolve,
        reject,
        options,
        retries: 0,
        timestamp: Date.now(),
      });

      this.processQueue();
    });
  }

  /**
   * 处理请求队列
   */
  async processQueue() {
    if (this.processing || this.requestQueue.length === 0) {
      return;
    }

    if (this.currentRequests >= this.maxConcurrentRequests) {
      return;
    }

    this.processing = true;
    const request = this.requestQueue.shift();
    this.currentRequests++;

    try {
      const startTime = Date.now();
      const result = await request.requestFn();

      // 更新统计信息
      this.updateStats(true, Date.now() - startTime);

      request.resolve(result);
    } catch (error) {
      console.error("❌ AI请求失败:", error);

      // 检查是否需要重试
      if (request.retries < this.maxRetries && this.shouldRetry(error)) {
        request.retries++;
        this.requestQueue.unshift(request); // 重新加入队列头部

        // 如果是限流错误，延迟处理
        if (this.isRateLimitError(error)) {
          this.stats.rateLimitHits++;
          await this.delay(this.rateLimitDelay);
        }
      } else {
        // 重试次数用完或不可重试错误，返回降级结果
        this.updateStats(false, 0);

        if (request.options.fallback) {
          request.resolve(request.options.fallback);
        } else {
          request.reject(error);
        }
      }
    } finally {
      this.currentRequests--;
      this.processing = false;

      // 继续处理队列
      setTimeout(() => this.processQueue(), 10);
    }
  }

  /**
   * 发起API请求（带重试和限流处理）
   */
  async makeAPIRequest(endpoint, data) {
    try {
      const response = await this.client.post(endpoint, data);
      return response;
    } catch (error) {
      // 处理特定错误类型
      if (error.response?.status === 429) {
        // 限流错误
        throw new Error("RATE_LIMIT");
      } else if (error.response?.status >= 500) {
        // 服务器错误，可重试
        throw new Error("SERVER_ERROR");
      } else {
        // 客户端错误，不重试
        throw error;
      }
    }
  }

  /**
   * 判断是否应该重试
   */
  shouldRetry(error) {
    const retryableErrors = ["RATE_LIMIT", "SERVER_ERROR", "TIMEOUT"];
    return (
      retryableErrors.includes(error.message) ||
      error.code === "ECONNRESET" ||
      error.code === "ETIMEDOUT"
    );
  }

  /**
   * 判断是否是限流错误
   */
  isRateLimitError(error) {
    return error.message === "RATE_LIMIT" || error.response?.status === 429;
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 更新统计信息
   */
  updateStats(success, responseTime) {
    this.stats.totalRequests++;

    if (success) {
      this.stats.successfulRequests++;

      // 计算平均响应时间
      const totalTime =
        this.stats.averageResponseTime * (this.stats.successfulRequests - 1) +
        responseTime;
      this.stats.averageResponseTime =
        totalTime / this.stats.successfulRequests;
    } else {
      this.stats.failedRequests++;
    }
  }

  /**
   * 构建意图分析提示词
   */
  buildIntentAnalysisPrompt(message, context) {
    let prompt = `你是一个专业的招聘助手意图分析器。请分析用户消息的意图类型。

用户消息: "${message}"

可能的意图类型包括：
- JOB_SEARCH: 搜索职位、找工作
- GREETING: 问候语、打招呼
- PROFILE_UPDATE: 更新个人档案、提供个人信息
- RECOMMENDATION_REQUEST: 请求推荐
- TECH_DIRECTION_INQUIRY: 技术方向询问
- SALARY_INQUIRY: 薪资询问
- COMPANY_INQUIRY: 公司询问
- UNKNOWN: 未知意图

请严格按照以下JSON格式返回分析结果，不要添加任何其他内容：
{
  "type": "意图类型",
  "confidence": 0.9,
  "entities": {},
  "context": {}
}`;

    if (context.sessionContext) {
      prompt += `\n\n会话上下文: ${JSON.stringify(context.sessionContext)}`;
    }

    if (context.messageHistory && context.messageHistory.length > 0) {
      prompt += `\n\n最近对话历史:\n`;
      context.messageHistory.forEach((msg, index) => {
        prompt += `${msg.message_type}: ${msg.message_content}\n`;
      });
    }

    return prompt;
  }

  /**
   * 获取意图分析系统提示词
   */
  getIntentAnalysisSystemPrompt() {
    return `你是一个专业的招聘助手意图分析器。请分析用户消息的意图类型。

可能的意图类型包括：
- greeting: 问候语
- profile_update: 更新个人档案
- job_search: 搜索职位
- recommendation_request: 请求推荐
- tech_direction_inquiry: 技术方向询问
- salary_inquiry: 薪资询问
- company_inquiry: 公司询问
- location_inquiry: 地点询问
- experience_inquiry: 经验询问
- resume_upload: 简历上传
- unknown: 未知意图

请以JSON格式返回分析结果：
{
  "type": "意图类型",
  "confidence": 0.0-1.0的置信度,
  "entities": {
    "技术栈": ["提取的技术"],
    "经验年限": "提取的年限",
    "期望薪资": "提取的薪资",
    "工作地点": "提取的地点"
  },
  "context": {
    "需要澄清": true/false,
    "澄清问题": "需要澄清的具体问题"
  }
}`;
  }

  /**
   * 解析意图分析结果
   */
  parseIntentAnalysisResult(result) {
    try {
      // 清理结果，移除markdown代码块标记
      let cleanResult = result.trim();
      if (cleanResult.startsWith("```json")) {
        cleanResult = cleanResult
          .replace(/^```json\s*/, "")
          .replace(/\s*```$/, "");
      } else if (cleanResult.startsWith("```")) {
        cleanResult = cleanResult.replace(/^```\s*/, "").replace(/\s*```$/, "");
      }

      // 修复常见的JSON格式问题
      cleanResult = cleanResult
        .replace(/,(\s*[}\]])/g, "$1") // 移除对象和数组末尾的多余逗号
        .replace(/'/g, '"'); // 将单引号替换为双引号

      // 尝试解析JSON
      const parsed = JSON.parse(cleanResult);

      return {
        type: parsed.type || "unknown",
        confidence: parsed.confidence || 0.5,
        entities: parsed.entities || {},
        context: parsed.context || {},
      };
    } catch (error) {
      console.error("❌ 解析意图分析结果失败:", error);

      // 回退到简单解析
      return {
        type: "unknown",
        confidence: 0.3,
        entities: {},
      };
    }
  }

  /**
   * 使用DeepSeek生成对话回复（带队列处理）
   */
  async generateResponseWithDeepSeek(prompt, context = {}) {
    this.modelStats.deepseek.requests++;
    const startTime = Date.now();

    return this.queueRequest(
      async () => {
        const messages = [
          { role: "system", content: this.getConversationSystemPrompt() },
          { role: "user", content: prompt },
        ];

        if (context.messageHistory) {
          const historyMessages = context.messageHistory
            .slice(-5)
            .map((msg) => ({
              role: msg.message_type === "user" ? "user" : "assistant",
              content: msg.message_content,
            }));
          messages.splice(1, 0, ...historyMessages);
        }

        const response = await this.makeAPIRequest("/chat/completions", {
          model: "deepseek-chat",
          messages: messages,
          max_tokens: this.maxTokens,
          temperature: this.temperature,
        });

        const responseTime = Date.now() - startTime;
        this.modelStats.deepseek.totalResponseTime += responseTime;
        this.modelStats.deepseek.averageResponseTime =
          this.modelStats.deepseek.totalResponseTime /
          this.modelStats.deepseek.requests;
        this.modelStats.deepseek.successRate = (
          ((this.modelStats.deepseek.requests -
            this.modelStats.deepseek.failures) /
            this.modelStats.deepseek.requests) *
          100
        ).toFixed(2);

        return response.data.choices[0].message.content;
      },
      {
        fallback: (() => {
          this.modelStats.deepseek.failures++;
          this.modelStats.deepseek.successRate = (
            ((this.modelStats.deepseek.requests -
              this.modelStats.deepseek.failures) /
              this.modelStats.deepseek.requests) *
            100
          ).toFixed(2);
          return "抱歉，我现在无法生成回复。请稍后再试。";
        })(),
      }
    );
  }

  /**
   * 获取对话系统提示词
   */
  getConversationSystemPrompt() {
    return `你是Katrina，一个专业的AI招聘助手。

【严格限制 - 绝对禁止的行为】：
1. 绝对不能虚构、编造或推荐具体的职位信息
2. 绝对不能提及具体的公司名称（除非用户主动提及）
3. 绝对不能给出具体的薪资数字或范围
4. 绝对不能编造招聘信息或职位详情

【你的唯一作用】：
1. 收集用户信息：技术栈、工作经验、期望薪资、工作地点等
2. 引导用户完善个人档案信息
3. 对用户提供的信息进行确认和澄清
4. 提供一般性的职业发展建议（不涉及具体职位）

【标准回复模式】：
- 当用户询问职位时，引导其提供个人信息
- 当信息不完整时，询问缺失的关键信息
- 当用户提供信息时，确认并询问是否还有补充

【回复原则】：
- 简洁、专业、友好
- 每次回复重点突出1-2个关键问题
- 避免长篇大论
- 不要主动提及具体的技术栈或公司

记住：你的作用是信息收集和引导，不是职位推荐！`;
  }

  /**
   * 检查服务状态
   */
  isReady() {
    return this.isInitialized;
  }

  /**
   * 获取服务统计信息
   */
  getStats() {
    return {
      initialized: this.isInitialized,
      endpoint: this.apiEndpoint,
      maxTokens: this.maxTokens,
      temperature: this.temperature,
      queue: {
        pending: this.requestQueue.length,
        processing: this.currentRequests,
        maxConcurrent: this.maxConcurrentRequests,
      },
      performance: {
        ...this.stats,
        successRate:
          this.stats.totalRequests > 0
            ? (
                (this.stats.successfulRequests / this.stats.totalRequests) *
                100
              ).toFixed(2) + "%"
            : "0%",
      },
    };
  }

  /**
   * 获取队列状态
   */
  getQueueStatus() {
    return {
      pending: this.requestQueue.length,
      processing: this.currentRequests,
      maxConcurrent: this.maxConcurrentRequests,
      isHealthy:
        this.requestQueue.length < 100 &&
        this.currentRequests < this.maxConcurrentRequests,
    };
  }

  /**
   * 使用Qwen进行意图分析
   */
  async analyzeIntentWithQwen(message, context) {
    try {
      this.modelStats.qwen.requests++;
      const startTime = Date.now();
      const prompt = this.buildIntentAnalysisPrompt(message, context);
      const response = await this.callQwenAPI(prompt, {
        max_tokens: 200,
        temperature: 0.1,
      });

      const responseTime = Date.now() - startTime;
      this.modelStats.qwen.totalResponseTime += responseTime;
      this.modelStats.qwen.averageResponseTime =
        this.modelStats.qwen.totalResponseTime / this.modelStats.qwen.requests;

      if (!response) {
        this.modelStats.qwen.failures++;
        return null;
      }

      const intentResult = this.parseIntentAnalysisResult(response);
      this.modelStats.qwen.successRate = (
        ((this.modelStats.qwen.requests - this.modelStats.qwen.failures) /
          this.modelStats.qwen.requests) *
        100
      ).toFixed(2);
      return intentResult;
    } catch (error) {
      console.error("❌ Qwen意图分析失败:", error.message);
      this.modelStats.qwen.failures++;
      this.modelStats.qwen.successRate = (
        ((this.modelStats.qwen.requests - this.modelStats.qwen.failures) /
          this.modelStats.qwen.requests) *
        100
      ).toFixed(2);
      return null;
    }
  }

  /**
   * 使用Qwen进行信息提取
   */
  async extractInfoWithQwen(message, userProfile) {
    try {
      this.modelStats.qwen.requests++;
      const startTime = Date.now();
      const prompt = this.buildInfoExtractionPrompt(message, userProfile);
      const response = await this.callQwenAPI(prompt, {
        max_tokens: 300,
        temperature: 0.1,
      });

      const responseTime = Date.now() - startTime;
      this.modelStats.qwen.totalResponseTime += responseTime;
      this.modelStats.qwen.averageResponseTime =
        this.modelStats.qwen.totalResponseTime / this.modelStats.qwen.requests;

      if (!response) {
        this.modelStats.qwen.failures++;
        return null;
      }

      const extractedInfo = this.parseInfoExtractionResult(response);
      this.modelStats.qwen.successRate = (
        ((this.modelStats.qwen.requests - this.modelStats.qwen.failures) /
          this.modelStats.qwen.requests) *
        100
      ).toFixed(2);
      return extractedInfo;
    } catch (error) {
      console.error("❌ Qwen信息提取失败:", error.message);
      this.modelStats.qwen.failures++;
      this.modelStats.qwen.successRate = (
        ((this.modelStats.qwen.requests - this.modelStats.qwen.failures) /
          this.modelStats.qwen.requests) *
        100
      ).toFixed(2);
      return null;
    }
  }

  // ==================== 缺失的核心方法 ====================

  /**
   * 调用Qwen API
   */
  async callQwenAPI(prompt, options = {}) {
    try {
      console.log("🤖 调用Qwen API:", prompt.substring(0, 100) + "...");

      if (!this.qwenConfig.apiKey) {
        throw new Error("Qwen API密钥未配置");
      }

      const requestBody = {
        model: this.qwenConfig.model,
        input: {
          messages: [
            {
              role: "user",
              content: prompt,
            },
          ],
        },
        parameters: {
          max_tokens: options.max_tokens || 500,
          temperature: options.temperature || 0.7,
          top_p: 0.8,
        },
      };

      const response = await axios.post(this.qwenConfig.endpoint, requestBody, {
        headers: {
          Authorization: `Bearer ${this.qwenConfig.apiKey}`,
          "Content-Type": "application/json",
        },
        timeout: this.qwenConfig.timeout,
      });

      // Qwen API的响应格式是 response.data.output.text
      if (response.data && response.data.output && response.data.output.text) {
        const content = response.data.output.text;
        console.log("✅ Qwen API调用成功，返回内容:", content);
        return content;
      } else {
        console.error("❌ Qwen API响应格式异常，实际响应:", response.data);
        throw new Error("Qwen API响应格式异常");
      }
    } catch (error) {
      console.error("❌ Qwen API调用失败:", error.message);
      throw error;
    }
  }

  /**
   * 构建信息提取提示词
   */
  buildInfoExtractionPrompt(message, userProfile) {
    const extractionFields = userProfile.extractionFields || [
      "技术方向",
      "所在公司",
      "当前职级",
      "期望薪资",
      "所在城市",
      "业务场景",
    ];

    return `请从以下用户消息中提取信息：

用户消息: "${message}"

需要提取的字段: ${extractionFields.join(", ")}

请以JSON格式返回提取到的信息，如果某个字段没有找到，请忽略该字段。

示例格式:
{
  "技术方向": "后端开发",
  "所在公司": "阿里巴巴",
  "当前职级": "P6"
}`;
  }

  /**
   * 解析信息提取结果
   */
  parseInfoExtractionResult(response) {
    try {
      // 尝试解析JSON响应
      const parsed = JSON.parse(response);
      return parsed;
    } catch (error) {
      console.warn("⚠️ 解析信息提取结果失败，使用正则提取:", error.message);

      // 回退到正则表达式提取
      const extracted = {};

      // 提取技术方向
      const techMatch = response.match(
        /技术方向[":：]\s*["']?([^"',\n]+)["']?/
      );
      if (techMatch) extracted.技术方向 = techMatch[1].trim();

      // 提取公司
      const companyMatch = response.match(
        /所在公司[":：]\s*["']?([^"',\n]+)["']?/
      );
      if (companyMatch) extracted.所在公司 = companyMatch[1].trim();

      // 提取职级
      const levelMatch = response.match(
        /当前职级[":：]\s*["']?([^"',\n]+)["']?/
      );
      if (levelMatch) extracted.当前职级 = levelMatch[1].trim();

      return extracted;
    }
  }
}

module.exports = AIServices;
