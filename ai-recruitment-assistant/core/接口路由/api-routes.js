/**
 * AI招聘助手系统 - API路由管理器
 *
 * 核心职责：
 * - RESTful API 定义
 * - 请求路由分发
 * - 中间件管理
 * - API 文档生成
 *
 * 预计代码量：1400行
 */

const express = require("express");
const { v4: uuidv4 } = require("uuid");

class ApiRoutes {
  constructor({ messageProcessor, userManager, database, config }) {
    this.messageProcessor = messageProcessor;
    this.userManager = userManager;
    this.database = database;
    this.config = config;
    this.router = express.Router();

    this.setupRoutes();
  }

  /**
   * 设置所有API路由
   */
  setupRoutes() {
    // 链接处理路由
    this.setupLinkRoutes();

    // 聊天相关路由
    this.setupChatRoutes();

    // 用户相关路由
    this.setupUserRoutes();

    // 职位相关路由
    this.setupJobRoutes();

    // 系统相关路由
    this.setupSystemRoutes();
  }

  /**
   * 设置链接处理路由
   */
  setupLinkRoutes() {
    // 处理聊天链接访问 - 支持明文邮箱格式
    this.router.get("/chat", async (req, res) => {
      try {
        const { email, name } = req.query;

        if (!email) {
          return res.status(400).json({
            error: "Missing email parameter",
            message: "缺少邮箱参数",
          });
        }

        console.log(
          `🔗 用户访问链接: email=${email}, name=${name || "unknown"}`
        );

        // 获取或创建用户
        const user = await this.userManager.getOrCreateUser(email);

        // 检查用户是否有活跃会话（14天内，实现跨设备同步）
        const activeSession = await this.findActiveUserSession(user.id);

        let session;
        if (activeSession) {
          // 使用现有会话（跨设备同步）
          session = activeSession;
          console.log(`♻️ 使用现有会话: ${session.session_uuid}`);

          // 更新会话活跃时间
          await this.database.updateSessionActivity(session.id);
        } else {
          // 创建新会话
          session = await this.database.createChatSession({
            userId: user.id,
            sessionUuid: uuidv4(),
            entrySourceUrl: req.headers.referer || "",
            initialIntent: "",
            context: {
              state: "initial",
              userName: name || "",
              lastIntent: null,
              profileCompleteness: 0,
            },
          });

          console.log(`🆕 创建新会话: ${session.session_uuid}`);

          // 发送个性化开场白
          await this.sendWelcomeMessage(session.id, name || "");
        }

        // 返回聊天界面HTML或重定向到前端
        res.json({
          success: true,
          sessionId: session.session_uuid,
          message: "会话已准备就绪",
          isNewSession: !activeSession,
        });
      } catch (error) {
        console.error("❌ 链接处理失败:", error);
        res.status(500).json({
          error: "Internal Server Error",
          message: "链接处理失败",
        });
      }
    });

    // 兼容根路径访问
    this.router.get("/", async (req, res) => {
      // 重定向到/chat路径
      const queryString = new URLSearchParams(req.query).toString();
      res.redirect(`/api/chat${queryString ? "?" + queryString : ""}`);
    });
  }

  /**
   * 查找用户的活跃会话（跨设备同步核心方法）
   */
  async findActiveUserSession(userId) {
    try {
      const fourteenDaysAgo = new Date();
      fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);

      const { data: sessions, error } = await this.database.client
        .from("chat_sessions")
        .select("*")
        .eq("user_id", userId)
        .gte("last_active_at", fourteenDaysAgo.toISOString())
        .order("last_active_at", { ascending: false })
        .limit(1);

      if (error) throw error;

      if (sessions && sessions.length > 0) {
        console.log(`🔄 找到活跃会话: ${sessions[0].session_uuid}`);
        return sessions[0];
      }

      // 如果没有活跃会话，清理过期数据
      await this.cleanExpiredUserSessions(userId, fourteenDaysAgo);
      return null;
    } catch (error) {
      console.error("❌ 查找活跃会话失败:", error);
      return null;
    }
  }

  /**
   * 清理用户的过期会话（14天清零）
   */
  async cleanExpiredUserSessions(userId, cutoffDate) {
    try {
      // 获取该用户的过期会话
      const { data: expiredSessions, error: sessionError } =
        await this.database.client
          .from("chat_sessions")
          .select("id")
          .eq("user_id", userId)
          .lt("last_active_at", cutoffDate.toISOString());

      if (sessionError) throw sessionError;

      if (expiredSessions && expiredSessions.length > 0) {
        const sessionIds = expiredSessions.map((s) => s.id);

        // 删除过期消息
        await this.database.client
          .from("chat_messages")
          .delete()
          .in("session_id", sessionIds);

        // 删除过期会话
        await this.database.client
          .from("chat_sessions")
          .delete()
          .in("id", sessionIds);

        console.log(`🗑️ 清理用户${userId}的${sessionIds.length}个过期会话`);
      }
    } catch (error) {
      console.error("❌ 清理过期会话失败:", error);
    }
  }
  async cleanExpiredUserSessions(userId, cutoffDate) {
    try {
      const fourteenDaysAgo = new Date();
      fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);

      const { data: sessions, error } = await this.database.client
        .from("chat_sessions")
        .select("*")
        .eq("user_id", userId)
        .gte("last_active_at", fourteenDaysAgo.toISOString())
        .order("last_active_at", { ascending: false })
        .limit(1);

      if (error) throw error;

      if (sessions && sessions.length > 0) {
        return sessions[0];
      }

      // 清理过期会话
      await this.cleanExpiredSessions(userId, fourteenDaysAgo);
      return null;
    } catch (error) {
      console.error("❌ 检查会话失败:", error);
      return null;
    }
  }

  /**
   * 清理过期会话和消息
   */
  async cleanExpiredSessions(userId, cutoffDate) {
    try {
      // 获取过期会话
      const { data: expiredSessions, error: sessionError } =
        await this.database.client
          .from("chat_sessions")
          .select("id")
          .eq("user_id", userId)
          .lt("last_active_at", cutoffDate.toISOString());

      if (sessionError) throw sessionError;

      if (expiredSessions && expiredSessions.length > 0) {
        const sessionIds = expiredSessions.map((s) => s.id);

        // 删除过期消息
        await this.database.client
          .from("chat_messages")
          .delete()
          .in("session_id", sessionIds);

        // 删除过期会话
        await this.database.client
          .from("chat_sessions")
          .delete()
          .in("id", sessionIds);

        console.log(`🗑️ 清理了 ${sessionIds.length} 个过期会话`);
      }
    } catch (error) {
      console.error("❌ 清理过期会话失败:", error);
    }
  }

  /**
   * 发送个性化欢迎消息
   */
  async sendWelcomeMessage(sessionId, userName) {
    try {
      const welcomeText = userName
        ? `您好${userName}，我是AI领域的猎头Katrina，专注于AI算法职位。
聊天框的左下角有简历上传的按钮，您可以分享最新的简历，便于我更精准的给您推荐职位。
同时，您也可以点击左下角的邮箱按钮，分享您的个人邮箱，我们可以更好的保持联系，定期的给您推送合适的职位。`
        : `您好，我是AI领域的猎头Katrina，专注于AI算法职位。
聊天框的左下角有简历上传的按钮，您可以分享最新的简历，便于我更精准的给您推荐职位。
同时，您也可以点击左下角的邮箱按钮，分享您的个人邮箱，我们可以更好的保持联系，定期的给您推送合适的职位。`;

      await this.database.saveChatMessage({
        sessionId: sessionId,
        messageType: "assistant",
        content: welcomeText,
        metadata: {
          type: "welcome",
          userName: userName,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error("❌ 发送欢迎消息失败:", error);
    }
  }

  /**
   * 设置聊天相关路由
   */
  setupChatRoutes() {
    // 发送消息
    this.router.post("/chat/message", async (req, res) => {
      try {
        const { message, sessionId, userEmail } = req.body;

        // 验证必需参数
        if (!message || !userEmail) {
          return res.status(400).json({
            error: "Missing required parameters",
            message: "缺少必需的参数：message 和 userEmail",
          });
        }

        // 处理消息
        const result = await this.messageProcessor.processMessage({
          message: message,
          sessionId: sessionId,
          userEmail: userEmail,
          entrySource: req.headers.referer || "",
        });

        res.json(result);
      } catch (error) {
        console.error("❌ 聊天消息处理失败:", error);
        res.status(500).json({
          error: "Internal Server Error",
          message: "消息处理失败",
        });
      }
    });

    // 获取后续消息
    this.router.post("/chat/follow-up", async (req, res) => {
      try {
        const { sessionId, userEmail } = req.body;

        // 验证必需参数
        if (!sessionId || !userEmail) {
          return res.status(400).json({
            success: false,
            error: "缺少必需参数",
          });
        }

        // 先通过UUID获取会话的数字ID
        const session = await this.database.getSessionByUuid(sessionId);
        if (!session) {
          return res.status(404).json({
            success: false,
            error: "会话不存在",
          });
        }

        console.log(
          `🔍 查询后续消息 - sessionUuid: ${sessionId}, sessionId: ${session.id}`
        );

        // 使用数字ID查询最新的后续消息（包括follow_up_message和job_recommendations）
        const { data: messages, error } = await this.database.client
          .from("chat_messages")
          .select("*")
          .eq("session_id", session.id)
          .eq("message_type", "assistant")
          .or(
            "metadata_json->>type.eq.follow_up_message,metadata_json->>type.eq.job_recommendations"
          )
          .order("timestamp", { ascending: false })
          .limit(1);

        if (error) {
          throw error;
        }

        console.log(`📨 查询到 ${messages?.length || 0} 条后续消息`);

        if (messages && messages.length > 0) {
          console.log(`✅ 返回后续消息: ${messages[0].message_content}`);
          res.json({
            success: true,
            response: {
              type: "follow_up",
              content: messages[0].message_content,
            },
          });
        } else {
          console.log("❌ 没有找到后续消息");
          res.json({
            success: true,
            response: null,
          });
        }
      } catch (error) {
        console.error("❌ 获取后续消息失败:", error);
        res.status(500).json({
          success: false,
          error: "获取后续消息失败",
        });
      }
    });

    // 获取聊天历史
    this.router.get("/chat/history/:sessionId", async (req, res) => {
      try {
        const { sessionId } = req.params;
        const { limit = 50 } = req.query;

        // 获取会话
        const session = await this.database.getSessionByUuid(sessionId);
        if (!session) {
          return res.status(404).json({
            error: "Session not found",
            message: "会话不存在",
          });
        }

        // 获取消息历史
        const messages = await this.database.getSessionMessages(
          session.id,
          parseInt(limit)
        );

        // 调试：打印消息顺序
        console.log("📋 聊天历史消息顺序:");
        messages.forEach((msg, index) => {
          console.log(
            `${index + 1}. [${msg.message_type}] ${msg.timestamp} - ${msg.message_content.substring(0, 30)}...`
          );
        });

        res.json({
          success: true,
          sessionId: sessionId,
          messages: messages,
          total: messages.length,
        });
      } catch (error) {
        console.error("❌ 获取聊天历史失败:", error);
        res.status(500).json({
          error: "Internal Server Error",
          message: "获取聊天历史失败",
        });
      }
    });

    // 创建新会话
    this.router.post("/chat/session", async (req, res) => {
      try {
        const { userEmail } = req.body;

        if (!userEmail) {
          return res.status(400).json({
            error: "Missing required parameter",
            message: "缺少必需的参数：userEmail",
          });
        }

        // 获取或创建用户
        const user = await this.userManager.getOrCreateUser(userEmail);

        // 创建新会话
        const session = await this.database.createChatSession({
          userId: user.id,
          sessionUuid: uuidv4(),
          entrySourceUrl: req.headers.referer || "",
          initialIntent: "",
          context: {
            state: "initial",
            lastIntent: null,
            profileCompleteness: 0,
          },
        });

        res.json({
          success: true,
          sessionId: session.session_uuid,
          userId: user.id,
          timestamp: session.created_at,
        });
      } catch (error) {
        console.error("❌ 创建会话失败:", error);
        res.status(500).json({
          error: "Internal Server Error",
          message: "创建会话失败",
        });
      }
    });
  }

  /**
   * 设置用户相关路由
   */
  setupUserRoutes() {
    // 获取用户档案
    this.router.get("/user/profile/:userEmail", async (req, res) => {
      try {
        const { userEmail } = req.params;

        // 获取用户
        const user = await this.database.getUserByEmail(userEmail);
        if (!user) {
          return res.status(404).json({
            error: "User not found",
            message: "用户不存在",
          });
        }

        // 获取候选人档案
        const profile = await this.database.getCandidateProfile(user.id);

        res.json({
          success: true,
          user: {
            id: user.id,
            email: user.email,
            userType: user.user_type,
            createdAt: user.created_at,
            lastLoginAt: user.last_login_at,
          },
          profile: profile,
        });
      } catch (error) {
        console.error("❌ 获取用户档案失败:", error);
        res.status(500).json({
          error: "Internal Server Error",
          message: "获取用户档案失败",
        });
      }
    });

    // 更新用户档案
    this.router.put("/user/profile/:userEmail", async (req, res) => {
      try {
        const { userEmail } = req.params;
        const profileData = req.body;

        // 获取用户
        const user = await this.database.getUserByEmail(userEmail);
        if (!user) {
          return res.status(404).json({
            error: "User not found",
            message: "用户不存在",
          });
        }

        // 更新档案
        const updatedProfile = await this.database.upsertCandidateProfile(
          user.id,
          profileData
        );

        res.json({
          success: true,
          profile: updatedProfile,
        });
      } catch (error) {
        console.error("❌ 更新用户档案失败:", error);
        res.status(500).json({
          error: "Internal Server Error",
          message: "更新用户档案失败",
        });
      }
    });
  }

  /**
   * 设置职位相关路由
   */
  setupJobRoutes() {
    // 搜索职位
    this.router.get("/jobs/search", async (req, res) => {
      try {
        const {
          techDirection,
          location,
          salaryMin,
          salaryMax,
          companyType,
          limit = 20,
        } = req.query;

        // 实现职位搜索逻辑
        // 这里暂时返回空结果，等待后续实现
        const jobs = [];

        res.json({
          success: true,
          jobs: jobs,
          total: jobs.length,
          filters: {
            techDirection,
            location,
            salaryMin,
            salaryMax,
            companyType,
          },
        });
      } catch (error) {
        console.error("❌ 搜索职位失败:", error);
        res.status(500).json({
          error: "Internal Server Error",
          message: "搜索职位失败",
        });
      }
    });

    // 获取职位详情
    this.router.get("/jobs/:jobId", async (req, res) => {
      try {
        const { jobId } = req.params;

        // 获取职位详情
        const { data, error } = await this.database.client
          .from("job_listings")
          .select(
            `
            *,
            companies (
              company_name,
              company_type,
              industry,
              logo_url
            )
          `
          )
          .eq("id", jobId)
          .single();

        if (error) throw error;

        if (!data) {
          return res.status(404).json({
            error: "Job not found",
            message: "职位不存在",
          });
        }

        res.json({
          success: true,
          job: data,
        });
      } catch (error) {
        console.error("❌ 获取职位详情失败:", error);
        res.status(500).json({
          error: "Internal Server Error",
          message: "获取职位详情失败",
        });
      }
    });
  }

  /**
   * 设置系统相关路由
   */
  setupSystemRoutes() {
    // 系统状态检查
    this.router.get("/system/status", async (req, res) => {
      try {
        const status = {
          timestamp: new Date().toISOString(),
          version: process.env.npm_package_version || "1.0.0",
          environment: this.config.getNodeEnv(),
          services: {
            database: this.database.isConnected,
            messageProcessor: this.messageProcessor.isReady
              ? this.messageProcessor.isReady()
              : false,
            userManager: this.userManager.isReady(),
            ai: this.messageProcessor.aiServices
              ? this.messageProcessor.aiServices.isReady()
              : false,
          },
        };

        res.json({
          success: true,
          status: status,
        });
      } catch (error) {
        console.error("❌ 获取系统状态失败:", error);
        res.status(500).json({
          error: "Internal Server Error",
          message: "获取系统状态失败",
        });
      }
    });

    // API文档
    this.router.get("/docs", (req, res) => {
      res.json({
        title: "AI招聘助手 API 文档",
        version: "1.0.0",
        endpoints: {
          chat: {
            "POST /api/chat/message": "发送聊天消息",
            "GET /api/chat/history/:sessionId": "获取聊天历史",
            "POST /api/chat/session": "创建新会话",
          },
          user: {
            "GET /api/user/profile/:userEmail": "获取用户档案",
            "PUT /api/user/profile/:userEmail": "更新用户档案",
          },
          jobs: {
            "GET /api/jobs/search": "搜索职位",
            "GET /api/jobs/:jobId": "获取职位详情",
          },
          system: {
            "GET /api/system/status": "系统状态检查",
            "GET /api/docs": "API文档",
          },
        },
      });
    });
  }

  /**
   * 获取路由器实例
   */
  getRouter() {
    return this.router;
  }
}

module.exports = ApiRoutes;
