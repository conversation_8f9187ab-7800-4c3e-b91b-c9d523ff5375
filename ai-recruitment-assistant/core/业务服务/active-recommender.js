/**
 * AI招聘助手系统 - 主动推荐引擎
 *
 * 核心职责：
 * - 响应用户主动需求
 * - 特定条件推荐
 * - 第二次推荐处理
 * - 推荐策略调整
 *
 * 预计代码量：1500行
 */

class ActiveRecommender {
  constructor(database, config) {
    this.database = database;
    this.config = config;
    this.isInitialized = false;
  }

  /**
   * 初始化主动推荐引擎
   */
  async initialize() {
    try {
      this.isInitialized = true;
      console.log("🚀 主动推荐引擎初始化完成");
    } catch (error) {
      console.error("❌ 主动推荐引擎初始化失败:", error);
      throw error;
    }
  }

  /**
   * 根据用户需求生成推荐
   */
  async generateRecommendationsByRequest(userId, request) {
    try {
      // 解析用户需求
      const parsedRequest = this.parseUserRequest(request);

      // 生成推荐
      const recommendations = await this.findMatchingJobs(
        userId,
        parsedRequest
      );

      return {
        success: true,
        recommendations: recommendations,
        request: parsedRequest,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("❌ 生成主动推荐失败:", error);
      throw error;
    }
  }

  /**
   * 解析用户请求
   */
  parseUserRequest(request) {
    // 解析用户的具体需求
    return {
      techDirection: null,
      salaryRange: null,
      location: null,
      companyType: null,
      experience: null,
    };
  }

  /**
   * 查找匹配的职位
   */
  async findMatchingJobs(userId, request) {
    try {
      // 实现职位匹配逻辑
      return [];
    } catch (error) {
      console.error("❌ 查找匹配职位失败:", error);
      return [];
    }
  }

  /**
   * 检查引擎状态
   */
  isReady() {
    return this.isInitialized;
  }

  // ==================== 职位推荐核心逻辑 ====================

  /**
   * 触发职位推荐
   */
  async triggerJobRecommendation(userId, infoState, session, userManager) {
    try {
      // 集成用户信息状态检查
      const userInfoStatus = this.checkUserInfoCompleteness(infoState);
      console.log("📊 用户信息完整度检查:", userInfoStatus);

      // 检查是否为第三方推荐请求
      const thirdPartyDetection = userManager.detectThirdPartyRequest(
        session.lastMessage || ""
      );
      if (thirdPartyDetection.isThirdParty) {
        console.log(
          "👥 检测到第三方推荐请求，目标对象:",
          thirdPartyDetection.targetRole
        );
        return await this.handleThirdPartyRecommendation(
          thirdPartyDetection,
          infoState,
          session
        );
      }

      // 检查是否是第二次推荐（精准推荐）
      const isPreciseRecommendation =
        userManager.checkPreciseRecommendationEligibility(infoState);

      // 生成4x4推荐矩阵
      let matrixData = null;
      if (userInfoStatus.completeness >= 0.6) {
        console.log("🎯 用户信息充足，生成4x4推荐矩阵");
        const userProfile = await this.buildUserProfileForMatrix(
          userId,
          infoState
        );
        matrixData =
          await this.passiveRecommender.generate4x4Matrix(userProfile);
        console.log(
          "📊 4x4矩阵生成完成，推荐位数量:",
          matrixData.metadata.totalCells
        );
      }

      // 实现第一次推荐和第二次精准推荐的区分
      if (isPreciseRecommendation) {
        console.log("🎯 触发第二次精准推荐");
        console.log("📈 精准推荐条件:", {
          hasResume: userInfoStatus.hasResume,
          infoCompleteness: userInfoStatus.completeness,
          previousRecommendations: userInfoStatus.previousRecommendations,
        });
        return await this.generatePreciseRecommendation(
          userId,
          infoState,
          matrixData
        );
      } else {
        console.log("🎯 触发第一次基础推荐");
        console.log("📋 基础推荐条件:", {
          techDirection: infoState.技术方向,
          hasBasicInfo: userInfoStatus.hasBasicInfo,
          recommendationType: "first_time",
        });
        return await this.generateBasicRecommendation(
          userId,
          infoState,
          session,
          matrixData
        );
      }
    } catch (error) {
      console.error("❌ 触发职位推荐失败:", error);
      return this.getErrorFallbackResponse();
    }
  }

  /**
   * 生成基础推荐（第一次推荐）
   */
  async generateBasicRecommendation(userId, infoState, session) {
    try {
      console.log("🔍 开始查询真实职位数据...");

      // 1. 映射技术方向到数据库ID
      const techDirectionId = await this.mapTechDirectionToId(
        infoState.技术方向
      );
      if (!techDirectionId) {
        console.error("❌ 无法映射技术方向:", infoState.技术方向);
        return this.getErrorFallbackResponse();
      }

      // 2. 查询匹配的职位（按4种公司类型分类）
      const jobsByCompanyType = await this.queryJobsByCompanyType(
        techDirectionId,
        infoState
      );

      // 3. 将职位数据扁平化为数组，便于后续查询详情
      const flatJobsList = [];
      Object.entries(jobsByCompanyType).forEach(([companyType, jobs]) => {
        jobs.forEach((job) => {
          flatJobsList.push(job);
        });
      });

      // 4. 生成推荐内容（使用格式化服务）
      const PassiveRecommender = require("./passive-recommender");

      const passiveRecommender = new PassiveRecommender(
        this.database,
        this.config
      );

      // 使用已初始化的AI服务实例
      const aiServices = require("../数据管理/ai-services");
      const aiInstance = new aiServices(this.config.getAIConfig());
      await aiInstance.initialize();

      const recommendationContent =
        await passiveRecommender.formatJobRecommendations(
          jobsByCompanyType,
          aiInstance
        );

      if (!recommendationContent) {
        return {
          type: "no_jobs_found",
          content:
            "抱歉，暂时没有找到完全匹配的职位，我会继续为您关注合适的机会。",
          metadata: {
            recommendationType: "basic",
            userInfo: infoState,
          },
        };
      }

      // 5. 将推荐的职位存储到会话上下文中，供后续查询详情使用
      await this.storeRecommendedJobs(session.session_uuid, flatJobsList);

      // 6. 延迟发送推荐职位详情
      setTimeout(async () => {
        try {
          await this.database.saveChatMessage({
            sessionId: session.id,
            messageType: "assistant",
            content: recommendationContent,
            metadata: {
              type: "job_recommendations",
              recommendationType: "basic",
              timestamp: new Date().toISOString(),
            },
          });
          console.log("✅ 职位推荐详情已发送");
        } catch (error) {
          console.error("❌ 发送职位推荐失败:", error);
        }
      }, 1000);

      return {
        type: "basic_recommendation",
        content: "好的，稍等，我查询一下职位库",
        metadata: {
          recommendationType: "basic",
          userInfo: infoState,
          hasFollowUpMessage: true,
        },
      };
    } catch (error) {
      console.error("❌ 生成基础推荐失败:", error);
      return this.getErrorFallbackResponse();
    }
  }

  /**
   * 生成精准推荐（第二次推荐）
   */
  async generatePreciseRecommendation(userId, infoState) {
    // TODO: 实现精准推荐逻辑 - 基于城市和业务场景的精准匹配
    return {
      type: "precise_recommendation",
      content: "好的，稍等，我再查询一下职位库",
      metadata: {
        recommendationType: "precise",
        userInfo: infoState,
        hasFollowUpMessage: true,
      },
    };
  }

  /**
   * 错误回退响应
   */
  getErrorFallbackResponse() {
    return {
      type: "error",
      content: "抱歉，系统遇到了一些技术问题，请稍后再试。",
      metadata: {
        error: true,
        timestamp: new Date().toISOString(),
      },
    };
  }

  // ==================== 数据查询方法 ====================

  /**
   * 映射技术方向到数据库ID
   */
  async mapTechDirectionToId(techDirection) {
    try {
      if (!techDirection) return null;

      const { data: techDirections, error } = await this.database.client
        .from("tech_tree")
        .select("id, tech_name")
        .ilike("tech_name", `%${techDirection}%`)
        .limit(1);

      if (error) {
        console.error("❌ 查询技术方向失败:", error);
        return null;
      }

      if (techDirections && techDirections.length > 0) {
        console.log(
          `✅ 技术方向映射: ${techDirection} → ID:${techDirections[0].id}`
        );
        return techDirections[0].id;
      }

      console.log(`⚠️ 未找到技术方向: ${techDirection}`);
      return null;
    } catch (error) {
      console.error("❌ 映射技术方向失败:", error);
      return null;
    }
  }

  /**
   * 按公司类型查询职位（4*4逻辑的核心）
   */
  async queryJobsByCompanyType(techDirectionId, infoState) {
    try {
      const companyTypes = ["头部大厂", "中型公司", "国企", "创业型公司"];
      const jobsByType = {};

      for (const companyType of companyTypes) {
        const jobs = await this.queryJobsByType(techDirectionId, companyType);
        if (jobs.length > 0) {
          jobsByType[companyType] = jobs;
          console.log(`✅ 找到${companyType}职位: ${jobs.length}个`);
        }
      }

      return jobsByType;
    } catch (error) {
      console.error("❌ 按公司类型查询职位失败:", error);
      return {};
    }
  }

  /**
   * 查询特定类型的职位
   */
  async queryJobsByType(techDirectionId, companyType) {
    try {
      const { data: jobs, error } = await this.database.client
        .from("job_listings")
        .select(
          `
          *,
          companies!inner(
            company_name,
            company_type
          )
        `
        )
        .eq("primary_tech_direction_id", techDirectionId)
        .eq("is_active", true)
        .eq("companies.company_type", companyType)
        .limit(1);

      if (error) {
        console.error(`❌ 查询${companyType}职位失败:`, error);
        return [];
      }

      return jobs || [];
    } catch (error) {
      console.error(`❌ 查询${companyType}职位异常:`, error);
      return [];
    }
  }

  /**
   * 存储推荐的职位到会话上下文
   */
  async storeRecommendedJobs(sessionUuid, jobsList) {
    try {
      // 获取会话ID
      const { data: session, error: sessionError } = await this.database.client
        .from("chat_sessions")
        .select("id")
        .eq("session_uuid", sessionUuid)
        .single();

      if (sessionError || !session) {
        console.error("❌ 获取会话失败:", sessionError);
        return false;
      }

      // 构建上下文更新
      const contextUpdate = {
        recommended_jobs: jobsList.map((job, index) => ({
          index: index + 1,
          job_id: job.id,
          company_name: job.companies?.company_name,
          job_title: job.job_title,
          stored_at: new Date().toISOString(),
        })),
        last_recommendation_time: new Date().toISOString(),
      };

      const { error: updateError } = await this.database.client
        .from("chat_sessions")
        .update({
          current_interaction_context: contextUpdate,
        })
        .eq("id", session.id);

      if (updateError) {
        console.error("❌ 更新会话上下文失败:", updateError);
        return false;
      }

      console.log(`✅ 已存储${jobsList.length}个推荐职位到会话上下文`);
      return true;
    } catch (error) {
      console.error("❌ 存储推荐职位失败:", error);
      return false;
    }
  }

  // 检查用户信息完整度
  checkUserInfoCompleteness(infoState) {
    const requiredFields = [
      "技术方向",
      "所在公司",
      "当前职级",
      "期望薪资",
      "所在城市",
    ];
    const providedFields = requiredFields.filter(
      (field) => infoState[field] && infoState[field] !== ""
    );
    const completeness = providedFields.length / requiredFields.length;

    return {
      completeness: completeness,
      hasBasicInfo: !!infoState.技术方向,
      hasResume: !!infoState.简历内容,
      previousRecommendations: infoState.推荐次数 || 0,
      providedFields: providedFields,
      missingFields: requiredFields.filter(
        (field) => !infoState[field] || infoState[field] === ""
      ),
    };
  }

  // 处理第三方推荐
  async handleThirdPartyRecommendation(
    thirdPartyDetection,
    infoState,
    session
  ) {
    console.log("🔄 处理第三方推荐请求");
    return {
      type: "third_party_recommendation",
      content:
        `我理解您是想为${thirdPartyDetection.targetRole}寻找工作机会。\n\n` +
        `为了提供更精准的推荐，请您提供一些关于${thirdPartyDetection.targetRole}的基本信息：\n` +
        `1. 技术方向（如：前端、后端、全栈等）\n` +
        `2. 工作经验年限\n` +
        `3. 期望的公司类型\n` +
        `4. 期望薪资范围\n\n` +
        `这样我就能为${thirdPartyDetection.targetRole}推荐最合适的职位了！`,
      metadata: {
        isThirdParty: true,
        targetRole: thirdPartyDetection.targetRole,
        timestamp: new Date().toISOString(),
      },
    };
  }

  // 构建用户画像用于矩阵生成
  async buildUserProfileForMatrix(userId, infoState) {
    return {
      candidate_tech_direction_raw: infoState.技术方向,
      candidate_level_raw: infoState.当前职级,
      current_company_name_raw: infoState.所在公司,
      expected_salary_raw: infoState.期望薪资,
      current_city_raw: infoState.所在城市,
      years_of_experience: 3, // 简化处理
    };
  }
}

module.exports = ActiveRecommender;
