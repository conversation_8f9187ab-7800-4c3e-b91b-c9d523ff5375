/**
 * AI招聘助手系统 - 被动推荐引擎
 *
 * 核心职责：
 * - 4x4 矩阵职位推荐
 * - 候选人档案分析
 * - 推荐算法实现
 * - 推荐缓存管理
 *
 * 预计代码量：1800行
 */

class PassiveRecommender {
  constructor(database, config) {
    this.database = database;
    this.config = config;
    this.isInitialized = false;

    // 4x4推荐矩阵配置
    this.matrix4x4 = {
      companyTypes: ["startup", "bigtech", "traditional", "foreign"],
      techDirections: ["frontend", "backend", "fullstack", "mobile"],
    };
  }

  /**
   * 初始化被动推荐引擎
   */
  async initialize() {
    try {
      // 加载推荐算法配置
      await this.loadRecommendationConfig();

      this.isInitialized = true;
      console.log("🎯 被动推荐引擎初始化完成");
    } catch (error) {
      console.error("❌ 被动推荐引擎初始化失败:", error);
      throw error;
    }
  }

  /**
   * 加载推荐配置
   */
  async loadRecommendationConfig() {
    // 加载推荐算法的各种配置参数
    this.recommendationConfig = {
      maxRecommendations: this.config.getBusinessConfig().maxRecommendations,
      scoreThreshold: 0.6,
      diversityFactor: 0.3,
    };
  }

  /**
   * 生成4x4矩阵推荐
   */
  async generate4x4Recommendations(userId) {
    try {
      // 获取用户档案
      const profile = await this.database.getCandidateProfile(userId);
      if (!profile) {
        throw new Error("用户档案不存在");
      }

      // 生成4x4矩阵推荐
      const recommendations =
        await this.calculateMatrixRecommendations(profile);

      return {
        success: true,
        recommendations: recommendations,
        matrix: this.matrix4x4,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("❌ 生成4x4推荐失败:", error);
      throw error;
    }
  }

  /**
   * 计算矩阵推荐
   */
  async calculateMatrixRecommendations(profile) {
    const recommendations = [];

    // 为每个公司类型和技术方向组合生成推荐
    for (const companyType of this.matrix4x4.companyTypes) {
      for (const techDirection of this.matrix4x4.techDirections) {
        const jobs = await this.findJobsForCell(
          profile,
          companyType,
          techDirection
        );

        if (jobs.length > 0) {
          recommendations.push({
            companyType: companyType,
            techDirection: techDirection,
            jobs: jobs.slice(0, 3), // 每个格子最多3个职位
            matchScore: this.calculateCellMatchScore(
              profile,
              companyType,
              techDirection
            ),
          });
        }
      }
    }

    return recommendations;
  }

  /**
   * 为特定格子查找职位
   */
  async findJobsForCell(profile, companyType, techDirection) {
    try {
      // 构建搜索条件
      const criteria = {
        limit: 5, // 每个格子最多5个职位
      };

      // 技术方向匹配
      if (profile.primary_tech_direction_id) {
        criteria.techDirectionId = profile.primary_tech_direction_id;
      }

      // 薪资范围匹配
      if (profile.expected_compensation_min) {
        criteria.salaryMin = profile.expected_compensation_min;
      }
      if (profile.expected_compensation_max) {
        criteria.salaryMax = profile.expected_compensation_max;
      }

      // 经验等级匹配
      if (profile.candidate_standard_level_min) {
        criteria.experienceMin = profile.candidate_standard_level_min;
      }
      if (profile.candidate_standard_level_max) {
        criteria.experienceMax = profile.candidate_standard_level_max;
      }

      // 使用数据库管理器搜索职位
      const jobs = await this.database.searchJobs(criteria);

      // 根据公司类型筛选
      const filteredJobs = jobs.filter((job) => {
        return (
          !companyType ||
          (job.companies && job.companies.company_type === companyType)
        );
      });

      return filteredJobs.slice(0, 3); // 返回前3个最匹配的职位
    } catch (error) {
      console.error("❌ 查找格子职位失败:", error);
      return [];
    }
  }

  /**
   * 计算格子匹配分数
   */
  calculateCellMatchScore(profile, companyType, techDirection) {
    // 基础匹配分数计算逻辑
    let score = 0.5; // 基础分数

    // 技术方向匹配
    if (profile.primary_tech_direction_id) {
      // 这里需要实现技术方向匹配逻辑
      score += 0.3;
    }

    // 公司类型偏好匹配
    // 这里需要实现公司类型偏好匹配逻辑
    score += 0.2;

    return Math.min(score, 1.0);
  }

  /**
   * 检查引擎状态
   */
  isReady() {
    return this.isInitialized;
  }

  // 生成4x4矩阵基础结构
  async generate4x4Matrix(userProfile, companyTypeFilter = null) {
    try {
      // 定义公司类型映射：A=头部大厂,B=国企,C=中型公司,D=创业型
      const companyTypeMapping = {
        A: { name: "头部大厂", type: "bigtech" },
        B: { name: "国企", type: "stateowned" },
        C: { name: "中型公司", type: "medium" },
        D: { name: "创业型", type: "startup" },
      };
      const techDirectionMapping = {
        1: { name: "前端开发", keywords: ["前端", "React", "Vue"] },
        2: { name: "后端开发", keywords: ["后端", "Java", "Python"] },
        3: { name: "全栈开发", keywords: ["全栈", "Node.js"] },
        4: { name: "移动开发", keywords: ["移动", "iOS", "Android"] },
      };

      // 实现公司类型过滤规则
      let filteredCompanyTypes;
      let filterRuleApplied = "default";

      if (companyTypeFilter) {
        // 解析过滤参数，支持排除多个类型
        const excludedTypes = Array.isArray(companyTypeFilter)
          ? companyTypeFilter
          : [companyTypeFilter];

        // 根据排除的类型确定过滤规则
        if (
          excludedTypes.includes("A") &&
          excludedTypes.includes("B") &&
          excludedTypes.includes("C")
        ) {
          // 缺失/排除[A,B,C]的规则：[D,D,D,D]
          filteredCompanyTypes = ["D", "D", "D", "D"];
          filterRuleApplied = "exclude_ABC";
        } else if (excludedTypes.includes("A") && excludedTypes.includes("B")) {
          // 缺失/排除[A,B]的规则：[C,C,C,D]
          filteredCompanyTypes = ["C", "C", "C", "D"];
          filterRuleApplied = "exclude_AB";
        } else if (excludedTypes.includes("A")) {
          // 缺失/排除[A]的规则：[B,C,D,B]
          filteredCompanyTypes = ["B", "C", "D", "B"];
          filterRuleApplied = "exclude_A";
        } else if (excludedTypes.includes("B")) {
          // 缺失/排除[B]的规则：[A,C,D,A]
          filteredCompanyTypes = ["A", "C", "D", "A"];
          filterRuleApplied = "exclude_B";
        } else {
          // 其他情况使用默认顺序
          filteredCompanyTypes = ["A", "B", "C", "D"];
          filterRuleApplied = "default_with_filter";
        }
      } else {
        // 默认无过滤的规则：[A,B,C,D]
        filteredCompanyTypes = ["A", "B", "C", "D"];
        filterRuleApplied = "no_filter";
      }

      // 应用过滤后的公司类型序列到矩阵生成
      // 确保矩阵的每一行使用正确的公司类型

      const matrix = [];
      const techDirections = Object.keys(techDirectionMapping);
      for (let i = 0; i < 4; i++) {
        const row = [];
        for (let j = 0; j < 4; j++) {
          const companyKey = filteredCompanyTypes[i];
          const techKey = techDirections[j];
          const companyInfo = companyTypeMapping[companyKey];
          const techInfo = techDirectionMapping[techKey];
          const matchScore = this.calculateMatrixCellScore(
            userProfile,
            companyInfo,
            techInfo
          );
          const cell = {
            position: { row: i, col: j },
            companyType: {
              key: companyKey,
              name: companyInfo.name,
              type: companyInfo.type,
            },
            techDirection: {
              key: techKey,
              name: techInfo.name,
              keywords: techInfo.keywords,
            },
            matchScore: matchScore,
            jobCount: 0,
            jobs: [],
            isRecommended: matchScore > 0.6,
          };
          row.push(cell);
        }
        matrix.push(row);
      }
      // 返回16个推荐位的数据结构
      return {
        matrix: matrix,
        metadata: {
          totalCells: 16,
          companyTypes: companyTypeMapping,
          techDirections: techDirectionMapping,
          filterRule: {
            applied: filterRuleApplied,
            filteredTypes: filteredCompanyTypes,
            originalFilter: companyTypeFilter,
          },
          userProfile: {
            techDirection: userProfile?.candidate_tech_direction_raw,
            level: userProfile?.candidate_level_raw,
          },
          generatedAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error("❌ 生成4x4矩阵失败:", error);
      throw error;
    }
  }

  calculateMatrixCellScore(userProfile, companyInfo, techInfo) {
    let score = 0.3;
    if (userProfile?.candidate_tech_direction_raw) {
      const userTech = userProfile.candidate_tech_direction_raw.toLowerCase();
      const techMatch = techInfo.keywords.some((keyword) =>
        userTech.includes(keyword.toLowerCase())
      );
      if (techMatch) score += 0.4;
    }
    if (userProfile?.current_company_name_raw) {
      const currentCompany = userProfile.current_company_name_raw.toLowerCase();
      if (
        companyInfo.type === "bigtech" &&
        (currentCompany.includes("腾讯") || currentCompany.includes("阿里"))
      ) {
        score += 0.2;
      }
    }
    if (userProfile?.candidate_level_raw) score += 0.1;
    return Math.min(score, 1.0);
  }

  // ==================== 职位格式化模块 ====================

  /**
   * 格式化职位推荐内容（优化版：AI摘要 + 简洁展示）
   */
  async formatJobRecommendations(jobsByCompanyType, aiServices) {
    try {
      if (!jobsByCompanyType || Object.keys(jobsByCompanyType).length === 0) {
        return null;
      }

      let content =
        "我刚刚快速的查询了一下，先给你推荐几个职位，你看看有没有感兴趣的。\n\n";
      let jobCount = 1;

      // 处理每个职位，生成AI摘要
      for (const [companyType, jobs] of Object.entries(jobsByCompanyType)) {
        for (const job of jobs) {
          // 安全获取公司名称
          const companyName = job.companies?.company_name || "未知公司";

          // 生成职位摘要
          const jobSummary = await this.generateJobSummary(job, aiServices);

          // 计算匹配度
          const matchScore = this.calculateMatchScore(job);

          // 修复职级显示逻辑
          let levelDisplay;
          if (
            job.job_level_raw &&
            job.job_level_raw.trim() &&
            job.job_level_raw !== "无"
          ) {
            levelDisplay = job.job_level_raw;
          } else if (job.job_standard_level_min && job.job_standard_level_max) {
            levelDisplay = `对标P${job.job_standard_level_min}-P${job.job_standard_level_max}`;
          } else if (job.job_standard_level_min) {
            levelDisplay = `对标P${job.job_standard_level_min}+`;
          } else {
            levelDisplay = "职级面议";
          }

          content += `${jobCount}. 🏢 ${companyName} - ${job.job_title || "未知职位"}\n`;
          content += `💼 ${levelDisplay} 📍 ${job.work_location || "地点待定"}\n`;
          content += `🎯 ${jobSummary}\n`;
          content += `⭐ 匹配度：${matchScore}%\n`;
          content += `回复"详情${jobCount}"查看完整职位信息\n\n`;
          jobCount++;
        }
      }

      content +=
        "您手头上有简历吗？麻烦您发一下给我，这样能够给您匹配更加精准的职位。";

      return content;
    } catch (error) {
      console.error("❌ 格式化职位推荐失败:", error);
      // 降级到原始格式
      return this.formatJobRecommendationsFallback(jobsByCompanyType);
    }
  }

  /**
   * 生成职位智能摘要
   */
  async generateJobSummary(job, aiServices) {
    try {
      const jobDescription = job.job_description || job.requirements || "";

      if (!jobDescription || jobDescription.length < 20) {
        return "详细要求请咨询";
      }

      // 构建AI摘要提示词
      const prompt = `请将以下职位描述总结为一句话的核心要点（不超过50字）：

职位描述：
${jobDescription}

要求：
1. 提取最核心的技术要求和职责
2. 突出亮点和特色
3. 语言简洁有力
4. 不超过50字

格式：直接输出摘要内容，不要其他说明`;

      // 调用AI服务生成摘要
      const summary = await aiServices.generateResponseWithDeepSeek(prompt, {
        maxTokens: 100,
        temperature: 0.3,
      });

      // 清理和验证摘要
      const cleanSummary = summary.trim().replace(/^["']|["']$/g, "");

      if (cleanSummary.length > 80) {
        return cleanSummary.substring(0, 77) + "...";
      }

      return cleanSummary || "详细要求请咨询";
    } catch (error) {
      console.error("❌ 生成职位摘要失败:", error);
      return "详细要求请咨询";
    }
  }

  /**
   * 计算职位匹配度
   */
  calculateMatchScore(job) {
    try {
      let score = 70; // 基础分数

      // 根据职位信息完整度调整分数
      if (job.job_description && job.job_description.length > 100) {
        score += 5;
      }

      if (job.job_level_raw && job.job_level_raw !== "无") {
        score += 5;
      }

      if (job.work_location && job.work_location !== "地点待定") {
        score += 5;
      }

      if (job.companies?.company_name) {
        score += 5;
      }

      // 确保分数在合理范围内
      return Math.min(Math.max(score, 60), 95);
    } catch (error) {
      console.error("❌ 计算匹配度失败:", error);
      return 75; // 默认分数
    }
  }

  /**
   * 降级格式化（原始版本，作为备用）
   */
  formatJobRecommendationsFallback(jobsByCompanyType) {
    try {
      let content =
        "我刚刚快速的查询了一下，先给你推荐几个职位，你看看有没有感兴趣的。\n\n";
      let jobCount = 1;

      Object.entries(jobsByCompanyType).forEach(([companyType, jobs]) => {
        jobs.forEach((job) => {
          const companyName = job.companies?.company_name || "未知公司";

          content += `公司${jobCount}：${companyName}\n`;
          content += `职位：${job.job_title || "未知职位"}\n`;
          content += `职级：${job.job_level_raw || `对标P${job.job_standard_level_min || "?"}-P${job.job_standard_level_max || "?"}`}\n`;
          content += `职位要点：详细要求请咨询\n\n`;
          jobCount++;
        });
      });

      content +=
        "您手头上有简历吗？麻烦您发一下给我，这样能够给您匹配更加精准的职位。";

      return content;
    } catch (error) {
      console.error("❌ 降级格式化失败:", error);
      return "抱歉，职位信息处理出现问题，请稍后再试。";
    }
  }
}

module.exports = PassiveRecommender;
